#!/usr/bin/env python3
"""
Complete Registration System Test
Tests the entire registration flow including frontend-backend integration
"""

import requests
import json
import sys
import time
from datetime import datetime

BASE_URL = "http://127.0.0.1:8000"
FRONTEND_URL = "http://localhost:5174"

def print_header(title):
    """Print a formatted header"""
    print("\n" + "="*60)
    print(f"🇰🇪 {title}")
    print("="*60)

def print_test_result(test_name, success, details=""):
    """Print test result with emoji"""
    emoji = "✅" if success else "❌"
    print(f"{emoji} {test_name}")
    if details:
        print(f"   {details}")

def test_backend_health():
    """Test if backend is running and healthy"""
    try:
        response = requests.get(f"{BASE_URL}/api/health/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print_test_result("Backend Health Check", True, 
                            f"Version: {data.get('version', 'Unknown')}")
            return True
        else:
            print_test_result("Backend Health Check", False, 
                            f"Status: {response.status_code}")
            return False
    except Exception as e:
        print_test_result("Backend Health Check", False, f"Error: {e}")
        return False

def test_counties_api():
    """Test counties API endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/api/locations/counties/", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if isinstance(data, dict) and 'results' in data:
                counties = data['results']
                print_test_result("Counties API", True, 
                                f"{len(counties)} counties loaded")
                return counties
            else:
                print_test_result("Counties API", False, "Invalid response format")
                return []
        else:
            print_test_result("Counties API", False, f"Status: {response.status_code}")
            return []
    except Exception as e:
        print_test_result("Counties API", False, f"Error: {e}")
        return []

def test_location_hierarchy(county_id):
    """Test location hierarchy API"""
    try:
        response = requests.get(
            f"{BASE_URL}/api/locations/hierarchy/?county_id={county_id}&type=sub_county",
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and 'locations' in data:
                locations = data['locations']
                print_test_result("Location Hierarchy", True, 
                                f"{len(locations)} sub-counties found")
                return locations
            else:
                print_test_result("Location Hierarchy", False, 
                                data.get('message', 'Unknown error'))
                return []
        else:
            print_test_result("Location Hierarchy", False, 
                            f"Status: {response.status_code}")
            return []
    except Exception as e:
        print_test_result("Location Hierarchy", False, f"Error: {e}")
        return []

def test_registration_api(county_id):
    """Test user registration API"""
    # Generate unique test data
    timestamp = int(time.time())
    test_data = {
        "national_id": f"{timestamp % 100000000:08d}",  # Ensure 8 digits
        "name": f"Test User {timestamp}",
        "email": f"test{timestamp}@example.com",
        "password": "testpass123",
        "county_id": county_id
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/register/",
            headers={"Content-Type": "application/json"},
            data=json.dumps(test_data),
            timeout=15
        )
        
        data = response.json()
        
        if response.status_code == 201 and data.get('success'):
            user = data.get('user', {})
            tokens = data.get('tokens', {})
            print_test_result("User Registration", True, 
                            f"User: {user.get('name', 'Unknown')}, "
                            f"County: {user.get('county_name', 'Unknown')}")
            
            # Test token validity
            if tokens.get('access') and tokens.get('refresh'):
                print_test_result("JWT Tokens", True, "Access and refresh tokens generated")
                return True, tokens
            else:
                print_test_result("JWT Tokens", False, "Tokens missing")
                return False, None
        else:
            print_test_result("User Registration", False, 
                            f"Status: {response.status_code}, "
                            f"Message: {data.get('message', 'Unknown error')}")
            if data.get('errors'):
                print(f"   Errors: {data['errors']}")
            return False, None
            
    except Exception as e:
        print_test_result("User Registration", False, f"Error: {e}")
        return False, None

def test_invalid_national_id():
    """Test registration with invalid National ID"""
    invalid_data = {
        "national_id": "123456",  # Only 6 digits - should fail
        "name": "Invalid Test User",
        "email": "<EMAIL>",
        "password": "testpass123",
        "county_id": 1
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/register/",
            headers={"Content-Type": "application/json"},
            data=json.dumps(invalid_data),
            timeout=10
        )
        
        data = response.json()
        
        if response.status_code == 400 and not data.get('success'):
            print_test_result("Invalid National ID Validation", True, 
                            "Correctly rejected invalid National ID")
            return True
        else:
            print_test_result("Invalid National ID Validation", False, 
                            f"Should have been rejected but got status: {response.status_code}")
            return False
            
    except Exception as e:
        print_test_result("Invalid National ID Validation", False, f"Error: {e}")
        return False

def test_frontend_accessibility():
    """Test if frontend is accessible"""
    try:
        response = requests.get(FRONTEND_URL, timeout=5)
        if response.status_code == 200:
            print_test_result("Frontend Accessibility", True, 
                            f"Frontend running on {FRONTEND_URL}")
            return True
        else:
            print_test_result("Frontend Accessibility", False, 
                            f"Status: {response.status_code}")
            return False
    except Exception as e:
        print_test_result("Frontend Accessibility", False, f"Error: {e}")
        return False

def test_cors_configuration():
    """Test CORS configuration"""
    try:
        response = requests.options(
            f"{BASE_URL}/api/locations/counties/",
            headers={
                "Origin": FRONTEND_URL,
                "Access-Control-Request-Method": "GET",
                "Access-Control-Request-Headers": "Content-Type"
            },
            timeout=5
        )
        
        cors_headers = {
            'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
            'access-control-allow-methods': response.headers.get('access-control-allow-methods'),
            'access-control-allow-headers': response.headers.get('access-control-allow-headers')
        }
        
        if any(cors_headers.values()):
            print_test_result("CORS Configuration", True, "CORS headers present")
            return True
        else:
            print_test_result("CORS Configuration", False, "No CORS headers found")
            return False
            
    except Exception as e:
        print_test_result("CORS Configuration", False, f"Error: {e}")
        return False

def main():
    """Run complete registration system test"""
    print_header("CivicAI Registration System Test Suite")
    print(f"Backend URL: {BASE_URL}")
    print(f"Frontend URL: {FRONTEND_URL}")
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests_passed = 0
    total_tests = 7
    
    # Test 1: Backend Health
    print_header("Backend Tests")
    if test_backend_health():
        tests_passed += 1
    
    # Test 2: Counties API
    counties = test_counties_api()
    if counties:
        tests_passed += 1
        
        # Test 3: Location Hierarchy (only if counties loaded)
        if test_location_hierarchy(counties[0]['id']):
            tests_passed += 1
        
        # Test 4: Valid Registration (only if counties loaded)
        success, tokens = test_registration_api(counties[0]['id'])
        if success:
            tests_passed += 1
    else:
        print_test_result("Location Hierarchy", False, "Skipped - no counties")
        print_test_result("User Registration", False, "Skipped - no counties")
    
    # Test 5: Invalid National ID
    if test_invalid_national_id():
        tests_passed += 1
    
    # Test 6: Frontend Accessibility
    print_header("Frontend Tests")
    if test_frontend_accessibility():
        tests_passed += 1
    
    # Test 7: CORS Configuration
    if test_cors_configuration():
        tests_passed += 1
    
    # Summary
    print_header("Test Summary")
    print(f"Tests Passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Registration system is fully functional.")
        print("\n📋 Next Steps:")
        print("1. Open your browser to: http://localhost:5174/register")
        print("2. Test the registration form manually")
        print("3. Check the dashboard after successful registration")
        return True
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure Django server is running: python manage.py runserver")
        print("2. Ensure frontend server is running: npm run dev")
        print("3. Check database connectivity")
        print("4. Verify CORS settings in Django")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
