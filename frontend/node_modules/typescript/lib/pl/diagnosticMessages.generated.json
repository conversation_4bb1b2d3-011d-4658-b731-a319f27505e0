{"ALL_COMPILER_OPTIONS_6917": "WSZYSTKIE OPCJE KOMPILATORA", "A_0_modifier_cannot_be_used_with_an_import_declaration_1079": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie można używać z deklaracją importu.", "A_0_parameter_must_be_the_first_parameter_2680": "Parametr „{0}” musi być pierwszym parametrem.", "A_JSDoc_template_tag_may_not_follow_a_typedef_callback_or_overload_tag_8039": "Tag „@template” obiektu JSDoc nie może następować po tagu „@typedef”, „@callback” ani „@overload”", "A_JSDoc_typedef_comment_may_not_contain_multiple_type_tags_8033": "Komentarz J<PERSON> „@typedef” nie może zawierać wielu tagów „@type”.", "A_bigint_literal_cannot_be_used_as_a_property_name_1539": "Literał „bigint” nie może być używany jako nazwa właściwości.", "A_bigint_literal_cannot_use_exponential_notation_1352": "Literał typu bigint nie może używać notacji wykładniczej.", "A_bigint_literal_must_be_an_integer_1353": "Literał typu bigint musi być liczbą całkowitą.", "A_binding_pattern_parameter_cannot_be_optional_in_an_implementation_signature_2463": "Parametr wzorca wiązania nie może być opcjonalny w sygnaturze implementacji.", "A_break_statement_can_only_be_used_within_an_enclosing_iteration_or_switch_statement_1105": "Instrukcji „break” można użyć tylko w ramach otaczającej instrukcji iteracji lub switch.", "A_break_statement_can_only_jump_to_a_label_of_an_enclosing_statement_1116": "Instrukcja „break” może wykonać skok tylko do etykiety otaczającej instrukcji.", "A_character_class_must_not_contain_a_reserved_double_punctuator_Did_you_mean_to_escape_it_with_backs_1522": "Klasa znaków nie może zawierać zastrzeżonej podwójnej interpunkcji. <PERSON>zy chodziło Ci o ucieczkę za pomocą ukośnika odwrotnego?", "A_character_class_range_must_not_be_bounded_by_another_character_class_1516": "Zakres klasy znaków nie może być ograniczony przez inną klasę znaków.", "A_class_can_only_implement_an_identifier_Slashqualified_name_with_optional_type_arguments_2500": "Klasa może zawierać implementację tylko identyfikatora/nazwy kwalifikowanej z opcjonalnymi argumentami typu.", "A_class_can_only_implement_an_object_type_or_intersection_of_object_types_with_statically_known_memb_2422": "Klasa może implementować tylko typ obiektu lub cz<PERSON><PERSON> wspólną typów obiektów ze statycznie znanymi elementami członkowskimi.", "A_class_cannot_extend_a_primitive_type_like_0_Classes_can_only_extend_constructable_values_2863": "<PERSON>lasa nie może rozszerzyć typu pier<PERSON>go, takiego jak „{0}”. Klasy mogą rozszerzać tylko wartości konstruowalne.", "A_class_cannot_implement_a_primitive_type_like_0_It_can_only_implement_other_named_object_types_2864": "Klasa nie może zaimplementować typu pierwotnego, takiego jak „{0}”. <PERSON><PERSON><PERSON> tylko inne nazwane typy obiektów.", "A_class_declaration_without_the_default_modifier_must_have_a_name_1211": "Deklaracja klasy bez modyfikatora „default” musi mieć nazwę.", "A_class_member_cannot_have_the_0_keyword_1248": "Składowa klasy nie może zawierać słowa kluczowego „{0}”.", "A_comma_expression_is_not_allowed_in_a_computed_property_name_1171": "Wyrażenie przecinkowe nie jest dozwolone w obliczonej nazwie właściwości.", "A_computed_property_name_cannot_reference_a_type_parameter_from_its_containing_type_2467": "Obliczona nazwa właściwości nie może odwoływać się do parametru typu z zawierającego go typu.", "A_computed_property_name_in_a_class_property_declaration_must_have_a_simple_literal_type_or_a_unique_1166": "Nazwa właściwości obliczanej w deklaracji właściwości klasy musi mieć typ prostego literału lub typ „unikatowy symbol”.", "A_computed_property_name_in_a_method_overload_must_refer_to_an_expression_whose_type_is_a_literal_ty_1168": "Nazwa właściwości obliczanej w przeciążeniu metody musi odwoływać się do wyrażenia, którego typem jest literał lub „unique symbol”.", "A_computed_property_name_in_a_type_literal_must_refer_to_an_expression_whose_type_is_a_literal_type__1170": "Nazwa właściwości obliczanej w typie literału musi odwoływać się do wyrażenia, którego typem jest literał lub „unique symbol”.", "A_computed_property_name_in_an_ambient_context_must_refer_to_an_expression_whose_type_is_a_literal_t_1165": "Nazwa właściwości obliczanej w otaczającym kontekście musi odwoływać się do wyrażenia, którego typem jest literał lub „unique symbol”.", "A_computed_property_name_in_an_interface_must_refer_to_an_expression_whose_type_is_a_literal_type_or_1169": "Nazwa właściwości obliczanej w interfejsie musi odwoływać się do wyrażenia, którego typem jest literał lub „unique symbol”.", "A_computed_property_name_must_be_of_type_string_number_symbol_or_any_2464": "Obliczona nazwa właściwości musi być typu „string”, „number”, „symbol” lub „any”.", "A_const_assertions_can_only_be_applied_to_references_to_enum_members_or_string_number_boolean_array__1355": "Aser<PERSON>je „const” mogą być stosowane tylko do odwołań do elementów członkowskich wyliczenia lub literałów typu string, number, boolean, array lub object.", "A_const_enum_member_can_only_be_accessed_using_a_string_literal_2476": "Dostęp do składowej wyliczenia ze specyfikatorem const można uzyskać tylko za pomocą literału ciągu.", "A_const_initializer_in_an_ambient_context_must_be_a_string_or_numeric_literal_or_literal_enum_refere_1254": "Inicjator „const” w otaczającym kontekście musi być ciągiem, literałem liczbowym albo odwołaniem do literału wyliczenia.", "A_constructor_cannot_contain_a_super_call_when_its_class_extends_null_17005": "Konstruktor nie może zawierać wywołania „super”, gdy jego klasa rozszerza wartość „null”.", "A_constructor_cannot_have_a_this_parameter_2681": "Konstruktor nie może zawierać parametru „this”.", "A_continue_statement_can_only_be_used_within_an_enclosing_iteration_statement_1104": "Instrukcji „continue” można użyć tylko w otaczającej instrukcji iteracji.", "A_continue_statement_can_only_jump_to_a_label_of_an_enclosing_iteration_statement_1115": "Instrukcja „continue” może wykonać skok tylko do etykiety otaczającej instrukcji iteracji.", "A_declaration_file_cannot_be_imported_without_import_type_Did_you_mean_to_import_an_implementation_f_2846": "Nie można zaimportować pliku deklaracji bez elementu „import type”. Czy zamiast tego chcesz zaimportować plik implementacji „{0}”?", "A_declare_modifier_cannot_be_used_in_an_already_ambient_context_1038": "Nie można użyć modyfikatora „declare” w otaczającym kontekście.", "A_decorator_can_only_decorate_a_method_implementation_not_an_overload_1249": "Dekorator może <PERSON> jedynie implementację metody, a nie przeciążenie.", "A_default_clause_cannot_appear_more_than_once_in_a_switch_statement_1113": "<PERSON><PERSON><PERSON><PERSON> „default” nie może występować więcej niż raz w instrukcji „switch”.", "A_default_export_can_only_be_used_in_an_ECMAScript_style_module_1319": "Eksport domyślny może być używany tylko w module w stylu języka ECMAScript.", "A_default_export_must_be_at_the_top_level_of_a_file_or_module_declaration_1258": "Eksport domyślny musi znajdować się na najwyższym poziomie deklaracji pliku lub modułu.", "A_definite_assignment_assertion_is_not_permitted_in_this_context_1255": "Asercja określonego przypisania „!” nie jest dozwolona w tym kontekście.", "A_destructuring_declaration_must_have_an_initializer_1182": "Deklaracja usuwająca strukturę musi mieć inicjator.", "A_dynamic_import_call_in_ES5_requires_the_Promise_constructor_Make_sure_you_have_a_declaration_for_t_2712": "Wywołanie dynamicznego importowania w języku ES5 wymaga konstruktora „Promise”.  Upewnij si<PERSON>, że masz deklarację dla konstruktora „Promise”, lub uw<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „ES2015” w opcji „--lib”.", "A_dynamic_import_call_returns_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_include_ES20_2711": "Wywołanie dynamicznego importowania zwraca element \"Promise\". <PERSON><PERSON><PERSON><PERSON>, że masz deklarację dla elementu \"Promise\" lub uwzgl<PERSON><PERSON><PERSON><PERSON> \"ES2015\" w opcji \"--lib\".", "A_file_cannot_have_a_reference_to_itself_1006": "Plik nie może przywoływać samego siebie.", "A_function_returning_never_cannot_have_a_reachable_end_point_2534": "Funkcja zwracająca war<PERSON> „never” nie może mieć osiągalnego punktu końcowego.", "A_function_that_is_called_with_the_new_keyword_cannot_have_a_this_type_that_is_void_2679": "Funkcja wywoływana ze słowem kluczowym „new” nie może mieć typu „this” o wartości „void”.", "A_function_whose_declared_type_is_neither_undefined_void_nor_any_must_return_a_value_2355": "<PERSON><PERSON><PERSON>, której zadeklarowany typ nie ma warto<PERSON> „undefined”, „void” ani „any”, musi zwrac<PERSON> warto<PERSON>.", "A_generator_cannot_have_a_void_type_annotation_2505": "Generator nie może mieć adnotacji typu „void”.", "A_get_accessor_cannot_have_parameters_1054": "Metoda dostępu „get” nie może mieć parametrów.", "A_get_accessor_must_be_at_least_as_accessible_as_the_setter_2808": "Metoda dostępu get musi być co najmniej tak samo dostępna, jak metoda ustawiająca", "A_get_accessor_must_return_a_value_2378": "Metoda dostępu „get” musi zwracać wartość.", "A_label_is_not_allowed_here_1344": "Etykieta nie jest dozwolona w tym mi<PERSON>.", "A_labeled_tuple_element_is_declared_as_optional_with_a_question_mark_after_the_name_and_before_the_c_5086": "Element krotki z etykietą jest deklarowany jako opcjonalny za pomocą znaku zapytania po nazwie i przed dwukropkiem, a nie po typie.", "A_labeled_tuple_element_is_declared_as_rest_with_a_before_the_name_rather_than_before_the_type_5087": "Oznaczony etykietą element krotki jest deklarowany jako reszta z \"...\" przed nazwą, a nie przed typem.", "A_mapped_type_may_not_declare_properties_or_methods_7061": "Zmapowany typ nie może deklarować właściwości ani metod.", "A_member_initializer_in_a_enum_declaration_cannot_reference_members_declared_after_it_including_memb_2651": "Inicjator składowej w deklaracji wyliczenia nie może przywoływać składowych zadeklarowanych po nim, w tym składowych zdefiniowanych w innych wyliczeniach.", "A_mixin_class_must_have_a_constructor_with_a_single_rest_parameter_of_type_any_2545": "Klasa mixin musi mieć konstruktor z pojedynczym parametrem rest o typie „any[]”.", "A_mixin_class_that_extends_from_a_type_variable_containing_an_abstract_construct_signature_must_also_2797": "<PERSON><PERSON><PERSON>, która rozciąga się od zmiennej typu zawierającej sygnaturę konstrukcji abstrakcyjnej, musi być również zadeklarowana jako „abstract”.", "A_module_cannot_have_multiple_default_exports_2528": "Moduł nie może mieć wielu eksportów domyślnych.", "A_namespace_declaration_cannot_be_in_a_different_file_from_a_class_or_function_with_which_it_is_merg_2433": "Deklaracja przestrzeni nazw nie może znajdować się w innym pliku niż klasa lub funkc<PERSON>, z którą ją scalono.", "A_namespace_declaration_cannot_be_located_prior_to_a_class_or_function_with_which_it_is_merged_2434": "Deklaracja przestrzeni nazw nie może występować przed klasą lub <PERSON>, z którą ją scalono.", "A_namespace_declaration_is_only_allowed_at_the_top_level_of_a_namespace_or_module_1235": "Deklaracja przestrzeni nazw jest dozwolona tylko na najwyższym poziomie przestrzeni nazw lub modułu.", "A_namespace_declaration_should_not_be_declared_using_the_module_keyword_Please_use_the_namespace_key_1540": "Deklaracja „namespace” nie powinna być deklarowana przy użyciu słowa kluczowego „module”. Zamiast tego należy użyć słowa kluczowego „namespace”.", "A_non_dry_build_would_build_project_0_6357": "Kompilacja inna niż -dry spowodowałaby skompilowanie projektu „{0}”", "A_non_dry_build_would_delete_the_following_files_Colon_0_6356": "Kompilacja inna niż -dry spowodowałaby usunięcie następujących plików: {0}", "A_non_dry_build_would_update_timestamps_for_output_of_project_0_6374": "Kompilacja bez opcji dry spowoduje zaktualizowanie sygnatur czasowych dla danych wyjściowych projektu „{0}”", "A_parameter_initializer_is_only_allowed_in_a_function_or_constructor_implementation_2371": "Inicjator parametru jest dozwolony tylko w implementacji funkcji lub konstruktora.", "A_parameter_property_cannot_be_declared_using_a_rest_parameter_1317": "Właściwości parametru nie można zadeklarować za pomocą parametru rest.", "A_parameter_property_is_only_allowed_in_a_constructor_implementation_2369": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> parametru jest dozwolona tylko w implementacji konstruktora.", "A_parameter_property_may_not_be_declared_using_a_binding_pattern_1187": "Właściwości parametru nie można zadeklarować za pomocą wzorca wiązania.", "A_promise_must_have_a_then_method_1059": "Obietnica musi mieć metodę „then”.", "A_property_of_a_class_whose_type_is_a_unique_symbol_type_must_be_both_static_and_readonly_1331": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> klasy, któ<PERSON>j typem jest „unique symbol”, musi być określona zarówno jako „static”, jak i „readonly”.", "A_property_of_an_interface_or_type_literal_whose_type_is_a_unique_symbol_type_must_be_readonly_1330": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> klasy, której typem jest literał lub „unique symbol”, musi być określona zarówno jako „static”, jak i „readonly”.", "A_required_element_cannot_follow_an_optional_element_1257": "Wymagany element nie może występować po elemencie opcjonalnym.", "A_required_parameter_cannot_follow_an_optional_parameter_1016": "Wymagany parametr nie może występować po opcjonalnym parametrze.", "A_rest_element_cannot_contain_a_binding_pattern_2501": "Element rest nie może zawierać wzorca wiązania.", "A_rest_element_cannot_follow_another_rest_element_1265": "Element rest nie może następować po innym elemencie rest.", "A_rest_element_cannot_have_a_property_name_2566": "Element rest nie może mieć nazwy właściwości.", "A_rest_element_cannot_have_an_initializer_1186": "Element rest nie może mieć inicjatora.", "A_rest_element_must_be_last_in_a_destructuring_pattern_2462": "Element rest musi być ostatni we wzorcu usuwającym strukturę.", "A_rest_element_type_must_be_an_array_type_2574": "Typ elementu rest musi być typem tablicowym.", "A_rest_parameter_cannot_be_optional_1047": "Parametr rest nie może być opcjonalny.", "A_rest_parameter_cannot_have_an_initializer_1048": "Parametr rest nie może mieć inicjatora.", "A_rest_parameter_must_be_last_in_a_parameter_list_1014": "Parametr rest musi występować na końcu listy parametrów.", "A_rest_parameter_must_be_of_an_array_type_2370": "Parametr rest musi być typu tablicowego.", "A_rest_parameter_or_binding_pattern_may_not_have_a_trailing_comma_1013": "Parametr rest ani wzorzec wiązania nie może mieć końcowego przecinka.", "A_return_statement_can_only_be_used_within_a_function_body_1108": "Instrukcji „return” można użyć tylko w treści funkcji.", "A_return_statement_cannot_be_used_inside_a_class_static_block_18041": "Instrukcji „return” nie można użyć wewnątrz bloku statycznego klasy.", "A_series_of_entries_which_re_map_imports_to_lookup_locations_relative_to_the_baseUrl_6167": "Seria w<PERSON>ów, które ponownie mapują importowane dane na lokalizacje wyszukiwania względne wobec adresu „baseUrl”.", "A_set_accessor_cannot_have_a_return_type_annotation_1095": "Metoda dostępu „set” nie może mieć adnotacji zwracanego typu.", "A_set_accessor_cannot_have_an_optional_parameter_1051": "Metoda dostępu „set” nie może mieć parametru opcjonalnego.", "A_set_accessor_cannot_have_rest_parameter_1053": "Met<PERSON> dostępu „set” nie może mieć parametru rest.", "A_set_accessor_must_have_exactly_one_parameter_1049": "Metoda dostępu „set” musi mieć dokładnie jeden parametr.", "A_set_accessor_parameter_cannot_have_an_initializer_1052": "Parametr metody <PERSON> „set” nie może mieć inicjatora.", "A_spread_argument_must_either_have_a_tuple_type_or_be_passed_to_a_rest_parameter_2556": "Argument nadlewki musi mieć typ krotki lub być przekazywany do parametru Rest.", "A_super_call_must_be_a_root_level_statement_within_a_constructor_of_a_derived_class_that_contains_in_2401": "Wywołanie „super” musi być instrukcją na poziomie głównym w konstruktorze klasy pochodnej, która zawiera zainicjowane właś<PERSON>ści, właściwości parametrów lub identyfikatory prywatne.", "A_super_call_must_be_the_first_statement_in_the_constructor_to_refer_to_super_or_this_when_a_derived_2376": "Wywołanie „super” musi być pierwszą instrukcją w konstruktorze, aby od<PERSON><PERSON><PERSON><PERSON><PERSON> się do „super” lub „this”, gdy klasa pochodna zawiera zainicjalizowane właściwości, właściwości parametrów lub prywatne identyfikatory.", "A_this_based_type_guard_is_not_compatible_with_a_parameter_based_type_guard_2518": "Ochrona typu oparta na elemencie „this” nie jest zgodna z ochroną typu opartą na parametrze.", "A_this_type_is_available_only_in_a_non_static_member_of_a_class_or_interface_2526": "Typ „this” jest dostępny tylko w niestatycznej składowej klasy lub interfejsu.", "A_top_level_export_modifier_cannot_be_used_on_value_declarations_in_a_CommonJS_module_when_verbatimM_1287": "Modyfikatora „export” najwyższego poziomu nie można używać w deklaracjach wartości w module CommonJS, gdy jest włączona opcja „verbatimModuleSyntax”.", "A_tsconfig_json_file_is_already_defined_at_Colon_0_5054": "<PERSON><PERSON> „tsconfig.json” jest już zdefiniowany w: „{0}”.", "A_tuple_member_cannot_be_both_optional_and_rest_5085": "Składowa krotki nie może być jednocześnie opcjonalna i typu rest.", "A_tuple_type_cannot_be_indexed_with_a_negative_value_2514": "Nie można indeksować typu krotki z wartością ujemną.", "A_type_assertion_expression_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_expression_Con_17007": "Wyrażenie asercji typu jest niedozwolone po lewej stronie wyrażenia potęgowania. Zastanów się nad zamknięciem wyrażenia w nawiasach.", "A_type_literal_property_cannot_have_an_initializer_1247": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ć literału typu nie może mieć inicjatora.", "A_type_only_import_can_specify_a_default_import_or_named_bindings_but_not_both_1363": "Import dotyczący tylko typu może określać import domyślny lub powiązania nazwane, ale nie jedno i drugie jednocześnie.", "A_type_predicate_cannot_reference_a_rest_parameter_1229": "Predykat typów nie może zawierać odwołania do parametru rest.", "A_type_predicate_cannot_reference_element_0_in_a_binding_pattern_1230": "Predykat typów nie może zawierać odwołania do elementu „{0}” we wzorcu wiązania.", "A_type_predicate_is_only_allowed_in_return_type_position_for_functions_and_methods_1228": "Predykat typów jest dozwolony tylko w położeniu zwracanego typu dla funkcji i metod.", "A_type_predicate_s_type_must_be_assignable_to_its_parameter_s_type_2677": "Musi być możliwe przypisanie typu predykatu typów do typu jego parametru.", "A_type_referenced_in_a_decorated_signature_must_be_imported_with_import_type_or_a_namespace_import_w_1272": "Typ przywoływany w sygnaturze dekorowanej musi zostać zaimportowany za pomocą elementu „import type” lub importu przestrzeni nazw, gdy są włączone elementy „isolatedModules” i „emitDecoratorMetadata”.", "A_variable_whose_type_is_a_unique_symbol_type_must_be_const_1332": "Zmienna, której typem „unique symbol”, musi być okre<PERSON>a jako „const”.", "A_yield_expression_is_only_allowed_in_a_generator_body_1163": "Wyrażenie „yield” jest dozwolone tylko w treści generatora.", "Abstract_method_0_in_class_1_cannot_be_accessed_via_super_expression_2513": "Nie można uzyskać dostępu do metody abstrakcyjnej „{0}” w klasie „{1}” za pomocą wyrażenia super.", "Abstract_methods_can_only_appear_within_an_abstract_class_1244": "<PERSON><PERSON> abstrakcyjne mogą występować tylko w klasie abstrakcyjnej.", "Abstract_properties_can_only_appear_within_an_abstract_class_1253": "Właściwości abstrakcyjne mogą występować tylko w klasie abstrakcyjnej.", "Abstract_property_0_in_class_1_cannot_be_accessed_in_the_constructor_2715": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> abstrakcyjna „{0}” w klasie „{1}” jest niedostępna w konstruktorze.", "Accessibility_modifier_already_seen_1028": "Napotkano ju<PERSON> modyfikat<PERSON>.", "Accessors_are_only_available_when_targeting_ECMAScript_5_and_higher_1056": "<PERSON><PERSON> dos<PERSON> są dostępne tylko w<PERSON>, gdy jest używany język ECMAScript 5 lub nowszy.", "Accessors_must_both_be_abstract_or_non_abstract_2676": "<PERSON><PERSON> metody dos<PERSON> muszą być abstrakcyjne lub nieabstrakcyjne.", "Add_0_to_unresolved_variable_90008": "Do<PERSON><PERSON> „{0}.” do nierozpoznanej zmiennej", "Add_a_return_statement_95111": "<PERSON><PERSON><PERSON> return", "Add_a_return_type_to_the_function_declaration_9031": "Dodaj zwracany typ do deklaracji funkcji.", "Add_a_return_type_to_the_function_expression_9030": "Dodaj zwracany typ do wyrażenia funkcji.", "Add_a_return_type_to_the_get_accessor_declaration_9032": "Dodaj zwracany typ do deklaracji metody dostępu get.", "Add_a_return_type_to_the_method_9034": "Dodawanie typu zwracanego do metody", "Add_a_type_annotation_to_the_parameter_0_9028": "<PERSON><PERSON><PERSON> typu do parametru {0}.", "Add_a_type_annotation_to_the_property_0_9029": "Do<PERSON>j adnotację typu do właściwości {0}.", "Add_a_type_annotation_to_the_variable_0_9027": "<PERSON><PERSON><PERSON> ad<PERSON>ację typu do zmiennej {0}.", "Add_a_type_to_parameter_of_the_set_accessor_declaration_9033": "Dodaj typ do parametru deklaracji metody dostępu zestawu.", "Add_all_missing_async_modifiers_95041": "Dodaj wszystkie brakujące modyfikatory „async”", "Add_all_missing_attributes_95168": "Dodawanie wszystkich brakujących atrybutów", "Add_all_missing_call_parentheses_95068": "Dodaj wszystkie brakujące nawiasy wywołań", "Add_all_missing_function_declarations_95157": "Dodaj wszystkie brakujące deklaracje funkcji", "Add_all_missing_imports_95064": "Dodaj wszystkie brakujące importy", "Add_all_missing_members_95022": "Dodaj wszystkie brakujące elementy członkowskie", "Add_all_missing_override_modifiers_95162": "Dodaj wszystkie brakujące modyfikatory „override”", "Add_all_missing_parameters_95190": "Dodaj wszystkie brakujące parametry", "Add_all_missing_properties_95166": "Dodaj wszystkie brakujące właściwości", "Add_all_missing_return_statement_95114": "Dodaj wszystkie brakujące instrukcje return", "Add_all_missing_super_calls_95039": "Dodaj wszystkie brakujące wywołania typu super", "Add_all_missing_type_annotations_90067": "Dodaj wszystkie brakujące adnotacje typu", "Add_all_optional_parameters_95193": "Dodaj wszystkie parametry opcjonalne", "Add_annotation_of_type_0_90062": "<PERSON><PERSON><PERSON> typu „{0}”", "Add_async_modifier_to_containing_function_90029": "<PERSON><PERSON><PERSON> modyfi<PERSON>or asynchroniczny do funkcji zawierającej", "Add_await_95083": "Dodaj operator „await”", "Add_await_to_initializer_for_0_95084": "Dodaj operator „await” do inicjatora dla elementu „{0}”", "Add_await_to_initializers_95089": "Dodaj operator „await” do inicjatorów", "Add_braces_to_arrow_function_95059": "Dodaj nawiasy klamrowe do funkcji strzałki", "Add_const_to_all_unresolved_variables_95082": "Dodaj element „const” do wszystkich nierozpoznanych zmiennych", "Add_const_to_unresolved_variable_95081": "Dodaj element „const” do nierozpoznanej zmiennej", "Add_definite_assignment_assertion_to_property_0_95020": "Dodaj asercję określonego przypisania do właściwości „{0}”", "Add_definite_assignment_assertions_to_all_uninitialized_properties_95028": "Dodaj asercję określonego przypisania do wszystkich niezainicjowanych właściwości", "Add_export_to_make_this_file_into_a_module_95097": "Dodaj element „export {}”, aby przeksz<PERSON>łcić ten plik w moduł", "Add_extends_constraint_2211": "<PERSON><PERSON>j <PERSON> „rozszerzeń”.", "Add_extends_constraint_to_all_type_parameters_2212": "Dodaj ograni<PERSON>enie „rozszerzeń” do wszystkich parametrów typu", "Add_import_from_0_90057": "Dodaj import z „{0}”", "Add_index_signature_for_property_0_90017": "Dodaj s<PERSON>ę indeksu dla właściwości „{0}”", "Add_initializer_to_property_0_95019": "Dodaj inicjator do właściwości „{0}”", "Add_initializers_to_all_uninitialized_properties_95027": "Dodaj inicjatory do wszystkich niezainicjowanych właściwości", "Add_missing_attributes_95167": "Dodawanie brakujących atrybutów", "Add_missing_call_parentheses_95067": "Dodaj brakujące nawiasy wywołań", "Add_missing_comma_for_object_member_completion_0_95187": "Dodaj brakujący przecinek dla ukończenia składowych obiektu „{0}”.", "Add_missing_enum_member_0_95063": "Dodaj brakujący element c<PERSON>łonkowski wyliczenia „{0}”", "Add_missing_function_declaration_0_95156": "<PERSON><PERSON><PERSON> de<PERSON> „{0}”", "Add_missing_new_operator_to_all_calls_95072": "Dodaj brakujący operator „new” do wszystkich wywołań", "Add_missing_new_operator_to_call_95071": "Dodaj brakujący operator „new” do wywołania", "Add_missing_parameter_to_0_95188": "<PERSON><PERSON><PERSON> parametr do elementu „{0}”", "Add_missing_parameters_to_0_95189": "<PERSON><PERSON><PERSON> parametry do elementu „{0}”", "Add_missing_properties_95165": "Dodaj <PERSON>kujące właściwości", "Add_missing_super_call_90001": "Dodaj brakujące wywołanie „super()”", "Add_missing_typeof_95052": "Dodaj brakujący element „typeof”", "Add_names_to_all_parameters_without_names_95073": "Dodaj nazwy do wszystkich parametrów bez nazw", "Add_optional_parameter_to_0_95191": "Dodaj opcjonalny parametr do elementu „{0}\"", "Add_optional_parameters_to_0_95192": "Dodaj opcjonalne parametry do elementu „{0}”", "Add_or_remove_braces_in_an_arrow_function_95058": "Dodaj lub usuń nawiasy klamrowe w funkcji strzałki", "Add_override_modifier_95160": "<PERSON><PERSON><PERSON> „override”", "Add_parameter_name_90034": "Dodaj nazwę parametru", "Add_qualifier_to_all_unresolved_variables_matching_a_member_name_95037": "Dodaj kwalifikator do wszystkich nierozpoznanych zmiennych pasujących do nazwy składowej", "Add_return_type_0_90063": "<PERSON><PERSON>j zwracany typ „{0}”", "Add_satisfies_and_a_type_assertion_to_this_expression_satisfies_T_as_T_to_make_the_type_explicit_9035": "<PERSON><PERSON><PERSON>y satisfies i asercję typu do tego wyrażenia (satisfies T jako T), aby <PERSON><PERSON> u<PERSON> typ.", "Add_satisfies_and_an_inline_type_assertion_with_0_90068": "<PERSON><PERSON><PERSON> elementy satisfies i asercję typu wbudowanego za pomocą elementu „{0}”", "Add_to_all_uncalled_decorators_95044": "Dodaj element „()” do wszystkich niewywoływanych dekoratorów", "Add_ts_ignore_to_all_error_messages_95042": "Dodaj element „@ts-ignore” do wszystkich komunikatów o błędach", "Add_undefined_to_a_type_when_accessed_using_an_index_6674": "Dodaj element „niezdefiniowany” do typu podczas uzyskiwania dostępu przy użyciu indeksu.", "Add_undefined_to_optional_property_type_95169": "Dodaj element \"undefined\" do opcjonalnego typu właściwości", "Add_undefined_type_to_all_uninitialized_properties_95029": "Dodaj typ nieokreślony do wszystkich niezainicjowanych właściwości", "Add_undefined_type_to_property_0_95018": "Dodaj typ „undefined” do właściwości „{0}”", "Add_unknown_conversion_for_non_overlapping_types_95069": "<PERSON><PERSON><PERSON> k<PERSON>ę „unknown” dla nienakładają<PERSON>ch się typów", "Add_unknown_to_all_conversions_of_non_overlapping_types_95070": "Dodaj element „unknown” do wszystkich konwersji nienakładających się typów", "Add_void_to_Promise_resolved_without_a_value_95143": "Dodaj argument „void” do obiektu Promise rozwiązanego bez wartości", "Add_void_to_all_Promises_resolved_without_a_value_95144": "Dodaj argument „void” do wszystkich obiektów Promise rozwiązanych bez wartości", "Adding_a_tsconfig_json_file_will_help_organize_projects_that_contain_both_TypeScript_and_JavaScript__5068": "Dodanie pliku tsconfig.json pomoże w organizowaniu projektów, które zawierają pliki TypeScript i JavaScript. Dowiedz się więcej: https://aka.ms/tsconfig.", "All_declarations_of_0_must_have_identical_constraints_2838": "Wszystkie deklaracje elementu „{0}” muszą mieć identyczne ograniczenia.", "All_declarations_of_0_must_have_identical_modifiers_2687": "Wszystkie deklaracje elementu „{0}” muszą mieć identyczne modyfikatory.", "All_declarations_of_0_must_have_identical_type_parameters_2428": "Wszystkie deklaracje „{0}” muszą mieć identyczne parametry typu.", "All_declarations_of_an_abstract_method_must_be_consecutive_2516": "Wszystkie deklaracje metody abstrakcyjnej muszą występować obok siebie.", "All_destructured_elements_are_unused_6198": "<PERSON><PERSON><PERSON><PERSON><PERSON> elementy, k<PERSON><PERSON><PERSON>ch strukturę usunię<PERSON>, są nieużywane.", "All_imports_in_import_declaration_are_unused_6192": "Wszystkie importy w deklaracji importu są nieużywane.", "All_type_parameters_are_unused_6205": "Wszystkie parametry typu są nieużywane.", "All_variables_are_unused_6199": "Wszystkie zmienne są nieużywane.", "Allow_JavaScript_files_to_be_a_part_of_your_program_Use_the_checkJS_option_to_get_errors_from_these__6600": "Zezwalaj plikom JavaScript na udział w programie. Użyj opcji „checkJS”, aby uzy<PERSON> błędy z tych plików.", "Allow_accessing_UMD_globals_from_modules_6602": "Zezwalaj na dostęp do zmiennych globalnych UMD z modułów.", "Allow_default_imports_from_modules_with_no_default_export_This_does_not_affect_code_emit_just_typech_6011": "Zezwalaj na domyślne importy z modułów bez domyślnego eksportu. To nie wpływa na emitowanie kodu, a tylko na sprawdzanie typów.", "Allow_import_x_from_y_when_a_module_doesn_t_have_a_default_export_6601": "Zezwalaj na elementy „import x from y”, gdy moduł nie ma domyślnej operacji eksportu.", "Allow_importing_helper_functions_from_tslib_once_per_project_instead_of_including_them_per_file_6639": "Zezwalaj na jednorazowe importowanie funkcji pomocniczych z biblioteki tslib w projekcie zamiast dołączania ich dla poszczególnych plików.", "Allow_imports_to_include_TypeScript_file_extensions_Requires_moduleResolution_bundler_and_either_noE_6407": "Zezwalaj na importowanie w celu uwzględnienia rozszerzeń plików TypeScript. Wymaga ustawienia elementu „--moduleResolution bundler” i „--noEmit” lub „--emitDeclarationOnly”.", "Allow_javascript_files_to_be_compiled_6102": "Zezwalaj na kompilowanie plików JavaScript.", "Allow_multiple_folders_to_be_treated_as_one_when_resolving_modules_6691": "Zezwalaj na traktowanie wielu folderów jako jednego podczas rozpoznawania modułów.", "Already_included_file_name_0_differs_from_file_name_1_only_in_casing_1261": "Już dołączona nazwa pliku „{0}” różni się od nazwy pliku „{1}” tylko wielkością liter", "Ambient_module_declaration_cannot_specify_relative_module_name_2436": "Deklaracja otaczającego modułu nie może określać względnej nazwy modułu.", "Ambient_modules_cannot_be_nested_in_other_modules_or_namespaces_2435": "Moduły otoczenia nie mogą być zagnieżdżone w innych modułach ani przestrzeniach nazw.", "An_AMD_module_cannot_have_multiple_name_assignments_2458": "Moduł AMD nie może mieć wielu przypisań nazw.", "An_abstract_accessor_cannot_have_an_implementation_1318": "Abstrakcyjna metoda dostępu nie może mieć implementacji.", "An_accessibility_modifier_cannot_be_used_with_a_private_identifier_18010": "Modyfikator dostępności nie może być używany z identyfikatorem prywatnym.", "An_accessor_cannot_have_type_parameters_1094": "Metoda dostępu nie może mieć parametrów typu.", "An_accessor_property_cannot_be_declared_optional_1276": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „accessor” nie może być zadeklarowana jako opcjonalna.", "An_ambient_module_declaration_is_only_allowed_at_the_top_level_in_a_file_1234": "Deklaracja otaczającego modułu jest dozwolona tylko na najwyższym poziomie pliku.", "An_argument_for_0_was_not_provided_6210": "<PERSON>e podano argumentu dla elementu „{0}”.", "An_argument_matching_this_binding_pattern_was_not_provided_6211": "Argument pasujący do tego wzorca powiązania nie został podany.", "An_arithmetic_operand_must_be_of_type_any_number_bigint_or_an_enum_type_2356": "Arytmetyczny operand musi być typu „any”, „number”, „bigint” lub typu wyliczeniowego.", "An_arrow_function_cannot_have_a_this_parameter_2730": "Funkcja strzałki nie może mieć parametru „this”.", "An_async_function_or_method_in_ES5_requires_the_Promise_constructor_Make_sure_you_have_a_declaration_2705": "Asynchroniczna funkcja lub metoda w języku ES5 wymaga konstruktora „Promise”.  Upewnij si<PERSON>, że masz deklarację dla konstruktora „Promise”, lub uw<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „ES2015” w opcji „--lib”.", "An_async_function_or_method_must_return_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_in_2697": "Asynchroniczna funkcja lub metoda musi zwrócić element \"Promise\". <PERSON><PERSON><PERSON><PERSON>, że masz deklarac<PERSON>ę dla elementu \"Promise\" lub uwzg<PERSON><PERSON><PERSON><PERSON><PERSON> \"ES2015\" w opcji \"--lib\".", "An_async_iterator_must_have_a_next_method_2519": "Iterator asynchroniczny musi mieć metodę „next()”.", "An_element_access_expression_should_take_an_argument_1011": "Wyrażenie dostępu do elementu powinno przyjmować argument.", "An_enum_member_cannot_be_named_with_a_private_identifier_18024": "Elementu członkowskiego wyliczenia nie można nazwać za pomocą identyfikatora prywatnego.", "An_enum_member_cannot_have_a_numeric_name_2452": "Składowa wyliczenia nie może mieć nazwy liczbowej.", "An_enum_member_name_must_be_followed_by_a_or_1357": "Po nazwie elementu członkowskiego wyliczenia musi następować znak „,”, „=” lub „}”.", "An_expanded_version_of_this_information_showing_all_possible_compiler_options_6928": "Rozszerzona wersja tych informacji, przedstawiająca wszystkie możliwe opcje kompilatora", "An_export_assignment_cannot_be_used_in_a_module_with_other_exported_elements_2309": "Nie można użyć przypisania eksportu w module z innymi eksportowanymi elementami.", "An_export_assignment_cannot_be_used_in_a_namespace_1063": "Nie można użyć przypisania eksportu w przestrzeni nazw.", "An_export_assignment_cannot_have_modifiers_1120": "Przypisanie eksportu nie może mieć modyfikatorów.", "An_export_assignment_must_be_at_the_top_level_of_a_file_or_module_declaration_1231": "Przypisanie eksportu musi znajdować się na najwyższym poziomie deklaracji pliku lub modułu.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_module_1474": "Deklaracji eksportu można używać tylko na najwyższym poziomie modułu.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1233": "Deklaracji eksportu można używać tylko na najwyższym poziomie przestrzeni nazw lub modułu.", "An_export_declaration_cannot_have_modifiers_1193": "Deklaracja eksportu nie może mieć modyfikatorów.", "An_export_declaration_must_reference_a_real_value_when_verbatimModuleSyntax_is_enabled_but_0_resolve_1283": "Deklaracja „export =” musi odwoływać się do rzeczywistej wartości, gdy w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „verbatimModuleSyntax” jest włą<PERSON><PERSON>, ale element „{0}” jest rozpoznawany jako deklaracja tylko typu.", "An_export_declaration_must_reference_a_value_when_verbatimModuleSyntax_is_enabled_but_0_only_refers__1282": "Deklaracja „export =” musi odwoływać się do wartości, gdy opcja „verbatimModuleSyntax” jest włączona, ale element „{0}” odwołuje się tylko do typu.", "An_export_default_must_reference_a_real_value_when_verbatimModuleSyntax_is_enabled_but_0_resolves_to_1285": "Element „export default” musi odwoływać się do rzeczywistej wartości, gdy opcja „verbatimModuleSyntax” jest włą<PERSON><PERSON>, ale element „{0}” jest rozpoznawany jako deklaracja tylko typu.", "An_export_default_must_reference_a_value_when_verbatimModuleSyntax_is_enabled_but_0_only_refers_to_a_1284": "Element „export default” musi odwoływać się do wartości, gdy opcja „verbatimModuleSyntax” jest włączona, ale element „{0}” odwołuje się tylko do typu.", "An_expression_of_type_void_cannot_be_tested_for_truthiness_1345": "Wyrażenie typu „void” nie może być testowane pod kątem prawdziwości.", "An_extended_Unicode_escape_value_must_be_between_0x0_and_0x10FFFF_inclusive_1198": "Rozszerzona wartość znaku ucieczki Unicode musi należeć do zakresu od 0x0 do 0x10FFFF (włącznie).", "An_identifier_or_keyword_cannot_immediately_follow_a_numeric_literal_1351": "Identyfikatora lub słowa kluczowego nie można użyć bezpośrednio po literale liczbowym.", "An_implementation_cannot_be_declared_in_ambient_contexts_1183": "Implementacja nie może być zadeklarowana w otaczających kontekstach.", "An_import_alias_cannot_reference_a_declaration_that_was_exported_using_export_type_1379": "Alias importu nie może odwoływać się do deklaracji, która została wyeksportowana przy użyciu elementu „export type”.", "An_import_alias_cannot_reference_a_declaration_that_was_imported_using_import_type_1380": "Alias importu nie może odwoływać się do deklaracji, która została zaimportowana przy użyciu elementu „import type”.", "An_import_alias_cannot_resolve_to_a_type_or_type_only_declaration_when_verbatimModuleSyntax_is_enabl_1288": "Alias importu nie może zostać rozpoznany jako deklaracja typu lub deklaracja tylko typu, gdy jest włączona opcja „verbatimModuleSyntax”.", "An_import_alias_cannot_use_import_type_1392": "Alias importu nie może używać właściwości „import type”", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_module_1473": "Deklaracji importu można używać tylko na najwyższym poziomie modułu.", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1232": "Deklaracji importu można używać tylko na najwyższym poziomie przestrzeni nazw lub modułu.", "An_import_declaration_cannot_have_modifiers_1191": "Deklaracja importu nie może mieć modyfikatorów.", "An_import_path_can_only_end_with_a_0_extension_when_allowImportingTsExtensions_is_enabled_5097": "Ścieżka importu może tylko kończyć się rozszerzeniem „{0}”, gdy jest włączona opcja „allowImportingTsExtensions”.", "An_index_signature_cannot_have_a_rest_parameter_1017": "Sygnatura indeksu nie może mieć parametru rest.", "An_index_signature_cannot_have_a_trailing_comma_1025": "Sygnatura indeksu nie może być zakończona przecinkiem.", "An_index_signature_must_have_a_type_annotation_1021": "Sygnatura indeksu musi mieć adnotację typu.", "An_index_signature_must_have_exactly_one_parameter_1096": "Sygnatura indeksu musi mieć dokładnie jeden parametr.", "An_index_signature_parameter_cannot_have_a_question_mark_1019": "Parametr sygnatury indeksu nie może zawierać znaku zapytania.", "An_index_signature_parameter_cannot_have_an_accessibility_modifier_1018": "Parametr sygnatury indeksu nie może mieć modyfikatora <PERSON>ę<PERSON>ści.", "An_index_signature_parameter_cannot_have_an_initializer_1020": "Parametr sygnatury indeksu nie może mieć inicjatora.", "An_index_signature_parameter_must_have_a_type_annotation_1022": "Parametr sygnatury indeksu musi mieć adnotację typu.", "An_index_signature_parameter_type_cannot_be_a_literal_type_or_generic_type_Consider_using_a_mapped_o_1337": "Typ parametru sygnatury indeksu nie może być typem literału ani typem ogólnym. Rozważ użycie zamiast tego mapowanego typu obiektu.", "An_index_signature_parameter_type_must_be_string_number_symbol_or_a_template_literal_type_1268": "Parametr sygnatury indeksu musi mieć typ „string”, „number” lub „symbol” albo typ literału szablonu.", "An_instantiation_expression_cannot_be_followed_by_a_property_access_1477": "Po wyrażeniu tworzenia wystąpienia nie może następować dostęp do właściwości.", "An_interface_can_only_extend_an_identifier_Slashqualified_name_with_optional_type_arguments_2499": "Interfejs może rozszerzać tylko identyfikator/nazwę kwalifikowaną z opcjonalnymi argumentami typu.", "An_interface_can_only_extend_an_object_type_or_intersection_of_object_types_with_statically_known_me_2312": "Interfejs może rozszerzać tylko typ obiektu lub cz<PERSON><PERSON><PERSON> wspólną typów obiektów ze statycznie znanymi elementami członkowskimi.", "An_interface_cannot_extend_a_primitive_type_like_0_It_can_only_extend_other_named_object_types_2840": "Interfejs nie może rozszerzyć typu pierwotnego, takiego jak „{0}”. <PERSON><PERSON><PERSON> rozszerzać tylko inne nazwane typy obiektów.", "An_interface_property_cannot_have_an_initializer_1246": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> interfejsu nie może mieć inicjatora.", "An_iterator_must_have_a_next_method_2489": "Iterator mus<PERSON> metodę „next()”.", "An_jsxFrag_pragma_is_required_when_using_an_jsx_pragma_with_JSX_fragments_17017": "W przypadku używania dyrektywy pragma @jsx z fragmentami JSX jest wymagana dyrektywa pragma @jsxFrag.", "An_object_literal_cannot_have_multiple_get_Slashset_accessors_with_the_same_name_1118": "Literał obiektu nie może mieć wielu metod dostępu pobierania/ustawiania o takiej samej nazwie.", "An_object_literal_cannot_have_multiple_properties_with_the_same_name_1117": "Literał obiektu nie może mieć wielu właściwości o tej samej nazwie.", "An_object_literal_cannot_have_property_and_accessor_with_the_same_name_1119": "Literał obiektu nie może mieć właściwości i metody dostępu o takiej samej nazwie.", "An_object_member_cannot_be_declared_optional_1162": "Składowa obiektu nie może być zadeklarowana jako opcjonalna.", "An_object_s_Symbol_hasInstance_method_must_return_a_boolean_value_for_it_to_be_used_on_the_right_han_2861": "Metoda „[Symbol.hasInstance]” obiektu musi zwracać wartość logiczną, aby mogła być używana po prawej stronie wyrażenia „instanceof”.", "An_optional_chain_cannot_contain_private_identifiers_18030": "Opcjonalny łańcuch nie może zawierać identyfikatorów prywatnych.", "An_optional_element_cannot_follow_a_rest_element_1266": "Element opcjonalny nie może następować po elemencie rest.", "An_outer_value_of_this_is_shadowed_by_this_container_2738": "Zewnę<PERSON><PERSON><PERSON> war<PERSON> parametru „this” jest zasłaniana przez ten kontener.", "An_overload_signature_cannot_be_declared_as_a_generator_1222": "Sygnatura przeciążenia nie może być zadeklarowana jako generator.", "An_unary_expression_with_the_0_operator_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_ex_17006": "Wyrażenie jednoargumentowe z operatorem „{0}” jest niedozwolone po lewej stronie wyrażenia potęgowania. Zastanów się nad zamknięciem wyrażenia w nawiasach.", "Annotate_everything_with_types_from_JSDoc_95043": "Adnotuj wszystko przy użyciu typów JSDoc", "Annotate_types_of_properties_expando_function_in_a_namespace_90071": "Adnotuj typy funkcji expando właściwości w przestrzeni nazw", "Annotate_with_type_from_JSDoc_95009": "Do<PERSON><PERSON> z typem z danych JSDoc", "Another_export_default_is_here_2753": "W tym miej<PERSON>cu jest kolejny element export default.", "Any_Unicode_property_that_would_possibly_match_more_than_a_single_character_is_only_available_when_t_1528": "Każda właściwość standardu Unicode, która może być zgodna z więcej niż jednym znakiem, jest dostę<PERSON>na tylko wtedy, gdy ustawiono flagę Unicode Sets (v).", "Anything_that_would_possibly_match_more_than_a_single_character_is_invalid_inside_a_negated_characte_1518": "<PERSON><PERSON><PERSON><PERSON><PERSON>, co może być zgodne z więcej niż pojedynczym znakiem, jest nieprawidłowe wewnątrz klasy znaków negowanych.", "Are_you_missing_a_semicolon_2734": "<PERSON><PERSON> brakuje średnika?", "Argument_expression_expected_1135": "Oczekiwano wyrażenia argumentu.", "Argument_for_0_option_must_be_Colon_1_6046": "Argumentem opcji „{0}” musi być: {1}.", "Argument_of_dynamic_import_cannot_be_spread_element_1325": "Argument importu dynamicznego nie może być elementem spread.", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_2345": "Nie można przypisać argumentu typu „{0}” do parametru typu „{1}”.", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_with_exactOptionalPropertyTypes_Colon_tr_2379": "Argumentu typu \"{0}\" nie można przypisać do parametru typu \"{1}\" o wartości \"exactOptionalPropertyTypes: true\". Rozważ dodanie elementu \"undefined\" do typów właściwości obiektu docelowego.", "Arguments_for_the_rest_parameter_0_were_not_provided_6236": "Nie podano argumentów dla parametru REST „{0}”.", "Array_element_destructuring_pattern_expected_1181": "Oczekiwano wzorca usuwającego strukturę elementu tablicy.", "Arrays_with_spread_elements_can_t_inferred_with_isolatedDeclarations_9018": "Tablic z elementami rozproszonymi nie można wywnioskować za pomocą wyrażenia --isolatedDeclarations.", "Assertions_require_every_name_in_the_call_target_to_be_declared_with_an_explicit_type_annotation_2775": "<PERSON><PERSON><PERSON>je wymagają, aby każda nazwa w celu wywołania była zadeklarowana z jawną adnotacją typu.", "Assertions_require_the_call_target_to_be_an_identifier_or_qualified_name_2776": "<PERSON><PERSON><PERSON><PERSON> wymagają, aby cel wywołania był identyfikatorem lub nazwą kwalifikowaną.", "Assigning_properties_to_functions_without_declaring_them_is_not_supported_with_isolatedDeclarations__9023": "Przypisywanie właściwości do funkcji bez deklarowania ich nie jest obsługiwane w przypadku opcji --isolatedDeclarations. Dodaj jawną deklarację właściwości przypisanych do tej funkcji.", "Asterisk_Slash_expected_1010": "Oczekiwano znaków „*/”.", "At_least_one_accessor_must_have_an_explicit_return_type_annotation_with_isolatedDeclarations_9009": "Co najmniej jedna metoda dostępu musi mieć jawną adnotację zwracanego typu z wyrażeniem --isolatedDeclarations.", "Augmentations_for_the_global_scope_can_only_be_directly_nested_in_external_modules_or_ambient_module_2669": "Rozszerzenia zakresu globalnego mogą być zagnieżdżane bezpośrednio jedynie w modułach zewnętrznych lub deklaracjach modułów otoczenia.", "Augmentations_for_the_global_scope_should_have_declare_modifier_unless_they_appear_in_already_ambien_2670": "Rozszerzenia zakresu globalnego muszą mieć modyfikator „declare”, chyba że znajdują się w już otaczającym kontekście.", "Auto_discovery_for_typings_is_enabled_in_project_0_Running_extra_resolution_pass_for_module_1_using__6140": "Automatyczne odnajdowanie operacji wpisywania zostało włączone w projekcie „{0}”. Trwa uruchamianie dodatkowego przejścia rozwiązania dla modułu „{1}” przy użyciu lokalizacji pamięci podręcznej „{2}”.", "BUILD_OPTIONS_6919": "OPCJE KOMPILACJI", "Backwards_Compatibility_6253": "Zgodność z poprzednimi wersjami", "Base_class_expressions_cannot_reference_class_type_parameters_2562": "Wyrażenia klasy bazowej nie mogą odwoływać się do parametrów typu klasy.", "Base_constructor_return_type_0_is_not_an_object_type_or_intersection_of_object_types_with_statically_2509": "Zwracany typ konstruktora bazowego „{0}” nie jest typem obiektu ani częścią wspólną typów obiektów ze statycznie znanymi elementami członkowskimi.", "Base_constructors_must_all_have_the_same_return_type_2510": "Wszystkie konstruktory podstawowe muszą mieć ten sam zwracany typ.", "Base_directory_to_resolve_non_absolute_module_names_6083": "Katalog podstawowy do rozpoznawania innych niż bezwzględne nazw modułów.", "BigInt_literals_are_not_available_when_targeting_lower_than_ES2020_2737": "Literały typu BigInt są ni<PERSON>, gdy wersja docel<PERSON> jest wcześniejsza niż ES2020.", "Binary_digit_expected_1177": "Oczeki<PERSON><PERSON> bitu.", "Binding_element_0_implicitly_has_an_1_type_7031": "Dla elementu powiązania „{0}” niejawnie określono typ „{1}”.", "Binding_elements_can_t_be_exported_directly_with_isolatedDeclarations_9019": "Elementów powiązania nie można eksportować bezpośrednio za pomocą wyrażenia --isolatedDeclarations.", "Block_scoped_variable_0_used_before_its_declaration_2448": "Zmienna „{0}” o zakresie bloku została użyta przed jej <PERSON>.", "Build_a_composite_project_in_the_working_directory_6925": "Skompiluj projekt złożony w katalogu roboczym.", "Build_all_projects_including_those_that_appear_to_be_up_to_date_6636": "Kompiluj wszystkie projekty, łącznie z tymi, które wydają się być aktualne.", "Build_one_or_more_projects_and_their_dependencies_if_out_of_date_6364": "Kompiluj co najmniej jeden projekt i jego zależności, je<PERSON><PERSON> są nieaktualne", "Build_option_0_requires_a_value_of_type_1_5073": "<PERSON><PERSON>ja kompilacji „{0}” wymaga wartości typu {1}.", "Building_project_0_6358": "Trwa kompilowanie projektu „{0}”...", "Built_in_iterators_are_instantiated_with_a_TReturn_type_of_undefined_instead_of_any_6720": "Wbudowane iteratory są tworzone przy użyciu typu „TReturn” o wartości „undefined” zamiast „any”.", "COMMAND_LINE_FLAGS_6921": "FLAGI WIERSZA POLECENIA", "COMMON_COMMANDS_6916": "TYPOWE POLECENIA", "COMMON_COMPILER_OPTIONS_6920": "TYPOWE OPCJE KOMPILATORA", "Call_decorator_expression_90028": "Wywołaj wyrażenie dekoratora", "Call_signature_return_types_0_and_1_are_incompatible_2202": "Zwracane typy sygnatur wywołania „{0}” i „{1}” są niezgodne.", "Call_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7020": "Dla sygnatury wywołania bez adnotacji zwracanego typu niejawnie określono zwracany typ „any”.", "Call_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2204": "Sygnatury wywołania bez argumentów mają niezgodne zwracane typy „{0}” i „{1}”.", "Can_only_convert_logical_AND_access_chains_95142": "Można konwertować tylko łańcuchy logiczne ORAZ łańcuchy dostępu", "Can_only_convert_named_export_95164": "Można przekonwertować tylko nazwany eksport", "Can_only_convert_property_with_modifier_95137": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> można skonwertować tylko za pomocą modyfikatora", "Can_only_convert_string_concatenations_and_string_literals_95154": "Można konwertować tylko połączenia ciągów i literały ciągów", "Cannot_access_0_1_because_0_is_a_type_but_not_a_namespace_Did_you_mean_to_retrieve_the_type_of_the_p_2713": "Nie można uzyskać dostępu do elementu „{0}.{1}”, ponieważ element „{0}” jest typem, ale nie przestrzenią nazw. <PERSON>zy chcesz pobrać typ wła<PERSON><PERSON><PERSON>ści „{1}” w lokalizacji „{0}” za pomocą elementu „{0}[„{1}”]”?", "Cannot_access_0_from_another_file_without_qualification_when_1_is_enabled_Use_2_instead_1281": "Nie można uzyskać dostępu do „{0}” z innego pliku bez kwalifikacji, gdy opcja „{1}” jest włączona. Zamiast tego użyj „{2}”.", "Cannot_access_ambient_const_enums_when_0_is_enabled_2748": "Nie można uzyskać dostępu do wyliczenia stałej otoczenia, gdy element „{0}” jest włączony.", "Cannot_assign_a_0_constructor_type_to_a_1_constructor_type_2672": "Nie można przypisać typu konstruktora „{0}” do typu konstruktora „{1}”.", "Cannot_assign_an_abstract_constructor_type_to_a_non_abstract_constructor_type_2517": "Nie można przypisać abstrakcyjnego typu konstruktora do nieabstrakcyjnego typu konstruktora.", "Cannot_assign_to_0_because_it_is_a_class_2629": "Nie można przypisać do elementu „{0}”, poni<PERSON><PERSON><PERSON> jest to klasa.", "Cannot_assign_to_0_because_it_is_a_constant_2588": "<PERSON>e można przypisać do elementu „{0}”, poni<PERSON><PERSON><PERSON> jest to stała.", "Cannot_assign_to_0_because_it_is_a_function_2630": "<PERSON>e można przypisać do elementu „{0}”, poni<PERSON><PERSON><PERSON> jest to <PERSON><PERSON><PERSON>.", "Cannot_assign_to_0_because_it_is_a_namespace_2631": "<PERSON>e można przypisać do elementu „{0}”, poniew<PERSON><PERSON> jest to przestrzeń nazw.", "Cannot_assign_to_0_because_it_is_a_read_only_property_2540": "Nie można przypisać do elementu „{0}”, poni<PERSON><PERSON><PERSON> jest to właściwość tylko do odczytu.", "Cannot_assign_to_0_because_it_is_an_enum_2628": "<PERSON>e można przypisać do elementu „{0}”, poni<PERSON><PERSON><PERSON> jest to wyliczenie.", "Cannot_assign_to_0_because_it_is_an_import_2632": "Nie można przypisać do elementu „{0}”, poniew<PERSON>ż jest to import.", "Cannot_assign_to_0_because_it_is_not_a_variable_2539": "Nie można przydzielić do elementu „{0}”, poniew<PERSON>ż nie jest to zmienna.", "Cannot_assign_to_private_method_0_Private_methods_are_not_writable_2803": "Nie można przypisać do metody prywatnej „{0}”. Metody prywatne nie są zapisywalne.", "Cannot_augment_module_0_because_it_resolves_to_a_non_module_entity_2671": "Nie można rozszerzyć modułu „{0}”, ponieważ rozpoznawany jest obiekt inny niż moduł.", "Cannot_augment_module_0_with_value_exports_because_it_resolves_to_a_non_module_entity_2649": "Nie można rozszerzyć modułu „{0}” za pośrednictwem operacji eksportu wartości, poni<PERSON><PERSON><PERSON> jest on rozpoznawany jako jednostka inna niż moduł.", "Cannot_compile_modules_using_option_0_unless_the_module_flag_is_amd_or_system_6131": "Nie można skompilować modułów za pomocą opcji „{0}”, chyba że flaga „--module” ma warto<PERSON> „amd” lub „system”.", "Cannot_create_an_instance_of_an_abstract_class_2511": "Nie można utworzyć wystąpienia klasy abstrakcyjnej.", "Cannot_delegate_iteration_to_value_because_the_next_method_of_its_iterator_expects_type_1_but_the_co_2766": "Nie można delegować iteracji do wartości, poni<PERSON><PERSON><PERSON> metoda „next” jej iteratora oczekuje typu „{1}”, ale zawierający generator będzie zawsze wysyłał „{0}”.", "Cannot_export_0_Only_local_declarations_can_be_exported_from_a_module_2661": "Nie można wyeksportować elementu „{0}”. Z modułu można eksportować jedynie lokalne deklaracje.", "Cannot_extend_a_class_0_Class_constructor_is_marked_as_private_2675": "<PERSON>e można rozszerzyć klasy „{0}”. Konstruktor klasy jest oznaczony jako p<PERSON>ny.", "Cannot_extend_an_interface_0_Did_you_mean_implements_2689": "Nie można rozszerzyć interfejsu „{0}”. <PERSON><PERSON>ził<PERSON> o „implements”?", "Cannot_find_a_tsconfig_json_file_at_the_current_directory_Colon_0_5081": "Nie można odnaleźć pliku tsconfig.json w bieżącym katalogu: {0}.", "Cannot_find_a_tsconfig_json_file_at_the_specified_directory_Colon_0_5057": "Nie można znaleźć pliku tsconfig.json w określonym katalogu: „{0}”.", "Cannot_find_global_type_0_2318": "Nie można odnaleźć typu globalnego „{0}”.", "Cannot_find_global_value_0_2468": "<PERSON>e można odnaleźć wartości globalnej „{0}”.", "Cannot_find_lib_definition_for_0_2726": "Nie można znaleźć definicji biblioteki dla elementu „{0}”.", "Cannot_find_lib_definition_for_0_Did_you_mean_1_2727": "Nie można znaleźć definicji biblioteki dla elementu „{0}”. <PERSON><PERSON> chodziło Ci o element „{1}”?", "Cannot_find_module_0_Consider_using_resolveJsonModule_to_import_module_with_json_extension_2732": "Nie można znaleźć modułu „{0}”. Rozważ użycie opcji „--resolveJsonModule” do importowania modułu z rozszerzeniem „.json”.", "Cannot_find_module_0_Did_you_mean_to_set_the_moduleResolution_option_to_nodenext_or_to_add_aliases_t_2792": "<PERSON>e można odnaleźć modułu „{0}”. <PERSON><PERSON> ch<PERSON><PERSON>ć opcję „moduleResolution” na wartość „nodenext”, czy dodać aliasy do opcji „paths”?", "Cannot_find_module_0_or_its_corresponding_type_declarations_2307": "<PERSON>e można odnale<PERSON>ć modułu „{0}” lub odpowiadają<PERSON>ch mu deklaracji typów.", "Cannot_find_name_0_2304": "Nie można odnaleźć nazwy „{0}”.", "Cannot_find_name_0_Did_you_mean_1_2552": "Nie można znaleźć nazwy „{0}”. <PERSON><PERSON> o „{1}”?", "Cannot_find_name_0_Did_you_mean_the_instance_member_this_0_2663": "Nie można znaleźć nazwy „{0}”. <PERSON><PERSON> chodziło o składową wystąpienia „this.{0}”?", "Cannot_find_name_0_Did_you_mean_the_static_member_1_0_2662": "Nie można znaleźć nazwy „{0}”. <PERSON><PERSON> chodziło o statyczną składową „{1}.{0}”?", "Cannot_find_name_0_Did_you_mean_to_write_this_in_an_async_function_2311": "<PERSON>e można odnaleźć nazwy \"{0}\". <PERSON><PERSON> chodził<PERSON>i o napisanie tego w funkcji asynchronicznej?", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2583": "Nie można odnaleźć nazwy \"{0}\". <PERSON><PERSON> ch<PERSON>z zmienić bibliotekę docelową? Spróbuj zmienić opcję kompilatora \"lib\" na wersję \"{1}\" lub nowszą.", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2584": "Nie można odnaleźć nazwy \"{0}\". <PERSON><PERSON> ch<PERSON>z zmienić bibliotekę docelową? Spróbuj zmienić opcję kompilatora \"lib\", aby uw<PERSON><PERSON><PERSON><PERSON><PERSON> element \"dom\".", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_Bun_Try_npm_i_save_dev_types_Slashbun_2867": "Nie można odnaleźć nazwy „{0}”. <PERSON><PERSON> z<PERSON>ć definicje typów dla usługi Bun? Spróbuj użyć polecenia „npm i --save-dev @types/bun”.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_Bun_Try_npm_i_save_dev_types_Slashbun_2868": "Nie można odnaleźć nazwy „{0}”. <PERSON><PERSON> z<PERSON>ć definicje typów dla usługi Bun? Spróbuj użyć polecenia „npm i --save-dev @types/bun”, a następnie dodaj ciąg „bun” do pola typów w pliku tsconfig.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2582": "Nie można odnaleźć nazwy „{0}”. <PERSON><PERSON>ć definicje typów dla modułu uruchamiającego testy? Spróbuj użyć polecenia „npm i --save-dev @types/jest” lub „npm i --save-dev @types/mocha”.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2593": "Nie można odnaleźć nazwy \"{0}\". <PERSON><PERSON> ch<PERSON>ć definicje typów dla modułu uruchamiającego testy? Spróbuj użyć polecenia \"npm i --save-dev @types/jest\" lub \"npm i --save-dev @types/mocha\", a następnie dodaj element \"jest\" lub \"mocha\" do pola types w pliku tsconfig.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2581": "Nie można odnaleźć nazwy „{0}”. <PERSON><PERSON> z<PERSON>ć definicje typów dla biblioteki jQuery? Spróbuj użyć polecenia „npm i --save-dev @types/jquery”.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2592": "<PERSON>e można odnaleźć nazwy \"{0}\". <PERSON><PERSON> ch<PERSON> z<PERSON>ć definicje typów dla biblioteki jQuery? Spróbuj użyć polecenia \"npm i --save-dev @types/jquery\", a następnie dodaj element \"jquery\" do pola types w pliku tsconfig.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2580": "Nie można odnaleźć nazwy „{0}”. <PERSON><PERSON> zainstal<PERSON>ć definicje typów dla środowiska Node? Spróbuj użyć polecenia „npm i --save-dev @types/node”.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2591": "Nie można odnaleźć nazwy \"{0}\". <PERSON><PERSON> zain<PERSON>ć definicje typów dla węzła? Spróbuj użyć polecenia \"npm i --save-dev @types/node\", a następnie dodaj element \"node\" do pola types w pliku tsconfig.", "Cannot_find_namespace_0_2503": "Nie można odnaleźć przestrzeni nazw „{0}”.", "Cannot_find_namespace_0_Did_you_mean_1_2833": "Nie można odnaleźć przestrzeni nazw „{0}”. <PERSON><PERSON>ził<PERSON> o „{1}”?", "Cannot_find_parameter_0_1225": "Nie można odnaleźć parametru „{0}”.", "Cannot_find_the_common_subdirectory_path_for_the_input_files_5009": "Nie można odnaleźć wspólnej ścieżki podkatalogu dla plików wejściowych.", "Cannot_find_type_definition_file_for_0_2688": "Nie można znaleźć pliku definicji typu dla elementu „{0}”.", "Cannot_import_type_declaration_files_Consider_importing_0_instead_of_1_6137": "Nie można zaimportować plików deklaracji typu. Rozważ zaimportowanie „{0}” zamiast „{1}”.", "Cannot_initialize_outer_scoped_variable_0_in_the_same_scope_as_block_scoped_declaration_1_2481": "<PERSON>e moż<PERSON> zain<PERSON> zmiennej „{0}” z zakresu zewnętrznego w tym samym zakresie co deklaracja „{1}” należąca do zakresu bloku.", "Cannot_invoke_an_object_which_is_possibly_null_2721": "<PERSON>e można wywołać obiektu, kt<PERSON><PERSON> ma prawdopodobnie wartość „null”.", "Cannot_invoke_an_object_which_is_possibly_null_or_undefined_2723": "<PERSON>e można wywołać obiektu, kt<PERSON><PERSON> ma prawdopodobnie wartość „null” lub „undefined”.", "Cannot_invoke_an_object_which_is_possibly_undefined_2722": "<PERSON>e można wywołać obiektu, kt<PERSON><PERSON> ma prawdopodobnie wartość „undefined”.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_destructuring__2765": "<PERSON><PERSON> mo<PERSON>na itero<PERSON>, p<PERSON><PERSON><PERSON><PERSON>oda „next” jej iteratora oczekuje typu „{1}”, ale destrukturyzacja tablicy będzie zawsze wysyłała „{0}”.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_spread_will_al_2764": "<PERSON><PERSON> można itero<PERSON>, p<PERSON><PERSON><PERSON><PERSON>oda „next” jej iteratora oczekuje typu „{1}”, ale rozkładanie tablicy będzie zawsze wysyłało „{0}”.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_for_of_will_always_s_2763": "<PERSON><PERSON> można itero<PERSON>, p<PERSON><PERSON><PERSON><PERSON>oda „next” jej iteratora oczekuje typu „{1}”, ale pętla for-of b<PERSON><PERSON><PERSON> zaws<PERSON> wysyłała „{0}”.", "Cannot_move_statements_to_the_selected_file_95183": "Nie można przenieść instrukcji do wybranego pliku", "Cannot_move_to_file_selected_file_is_invalid_95179": "Nie można przenieść do pliku, wybrany plik jest nieprawidłowy", "Cannot_read_file_0_5083": "Nie można odczytać pliku „{0}”.", "Cannot_read_file_0_Colon_1_5012": "<PERSON>e można odczytać pliku „{0}”: {1}.", "Cannot_redeclare_block_scoped_variable_0_2451": "Nie można ponownie zadeklarować zmiennej „{0}” o zakresie bloku.", "Cannot_redeclare_exported_variable_0_2323": "<PERSON>e można zadeklarować ponownie wyeksportowanej zmiennej „{0}”.", "Cannot_redeclare_identifier_0_in_catch_clause_2492": "Nie można ponownie zadeklarować identyfikatora „{0}” w klauzuli catch.", "Cannot_start_a_function_call_in_a_type_annotation_1441": "Nie można uruchomić wywołania funkcji w adnotacji typu.", "Cannot_use_JSX_unless_the_jsx_flag_is_provided_17004": "Nie można użyć kodu JSX, jeśli nie podano flagi „--jsx”.", "Cannot_use_export_import_on_a_type_or_type_only_namespace_when_0_is_enabled_1269": "Nie można użyć polecenia „export import” w przestrzeni nazw typu lub tylko typu, gdy opcja „{0}” jest włącz<PERSON>.", "Cannot_use_imports_exports_or_module_augmentations_when_module_is_none_1148": "Nie można używać importów, eksportów lub rozszerzeń modułów, je<PERSON><PERSON> flaga „--module” ma warto<PERSON> „none”.", "Cannot_use_namespace_0_as_a_type_2709": "Nie można używać przestrzeni nazw „{0}” jako typu.", "Cannot_use_namespace_0_as_a_value_2708": "Nie można używać przestrzeni nazw „{0}” jak<PERSON>.", "Cannot_use_this_in_a_static_property_initializer_of_a_decorated_class_2816": "Nie można użyć elementu \"this\" w statycznym inicjatorze właściwości dekorowanej klasy.", "Cannot_write_file_0_because_it_will_overwrite_tsbuildinfo_file_generated_by_referenced_project_1_6377": "<PERSON>e można zapisać pliku „{0}”, poniew<PERSON>ż spowoduje to zastąpienie pliku „.tsbuildinfo” generowanego przez przywoływany projekt „{1}”", "Cannot_write_file_0_because_it_would_be_overwritten_by_multiple_input_files_5056": "<PERSON>e można zap<PERSON>ć pliku „{0}”, ponieważ zostałby nadpisany przez wiele plików wejściowych.", "Cannot_write_file_0_because_it_would_overwrite_input_file_5055": "<PERSON>e można zapisać pliku „{0}”, ponieważ nadpisałby plik wejściowy.", "Catch_clause_variable_cannot_have_an_initializer_1197": "Z<PERSON>nna klauzuli catch nie może mie<PERSON> inicjatora.", "Catch_clause_variable_type_annotation_must_be_any_or_unknown_if_specified_1196": "Adnotacja typu zmiennej w klauzuli catch musi mieć warto<PERSON>ć „any” lub „unknown”, jeśli jest okre<PERSON>.", "Change_0_to_1_90014": "Zmień element „{0}” na „{1}”", "Change_all_extended_interfaces_to_implements_95038": "Zmień wszystkie rozszerzone interfejsy na elementy „implements”", "Change_all_jsdoc_style_types_to_TypeScript_95030": "Zmień wszystkie typy w stylu JSDoc na TypeScript", "Change_all_jsdoc_style_types_to_TypeScript_and_add_undefined_to_nullable_types_95031": "Zmień wszystkie typy w stylu JSDoc na TypeScript (i dodaj element „| undefined” do typów dopuszczających wartość null)", "Change_extends_to_implements_90003": "Zmień atrybut „extends” na „implements”", "Change_spelling_to_0_90022": "Zmień pisownię na „{0}”", "Check_for_class_properties_that_are_declared_but_not_set_in_the_constructor_6700": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy istnieją zadeklarowane właściwości klas, które nie zostały ustawione w konstruktorze.", "Check_side_effect_imports_6806": "Sprawdź importy efektów ubocznych.", "Check_that_the_arguments_for_bind_call_and_apply_methods_match_the_original_function_6697": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy <PERSON>y dla metod „bind”, „call” i „apply” są zgodne z oryginalną funkcją.", "Checking_if_0_is_the_longest_matching_prefix_for_1_2_6104": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, czy „{0}” to najdłuższy zgodny prefiks dla „{1}” — „{2}”.", "Circular_definition_of_import_alias_0_2303": "Definicja cykliczna aliasu importu „{0}”.", "Circularity_detected_while_resolving_configuration_Colon_0_18000": "<PERSON><PERSON>ry<PERSON> podczas rozpoznawania konfiguracji: {0}", "Circularity_originates_in_type_at_this_location_2751": "Cykliczność pochodzi z typu w tym miej<PERSON>cu.", "Class_0_defines_instance_member_accessor_1_but_extended_class_2_defines_it_as_instance_member_functi_2426": "<PERSON><PERSON><PERSON> „{0}” definiuje metodę dostępu do składowej wystąpienia „{1}”, ale rozszerzona klasa „{2}” definiuje ją jako funkcję składową wystąpienia.", "Class_0_defines_instance_member_function_1_but_extended_class_2_defines_it_as_instance_member_access_2423": "<PERSON><PERSON><PERSON> „{0}” definiuje funkcję składową wystąpienia „{1}”, ale rozszerzona klasa „{2}” definiuje ją jako metodę dostępu do składowej wystąpienia.", "Class_0_defines_instance_member_property_1_but_extended_class_2_defines_it_as_instance_member_functi_2425": "<PERSON><PERSON><PERSON> „{0}” definiuje właściwość składowej wystąpienia „{1}”, ale rozszerzona klasa „{2}” definiuje ją jako funkcję składową wystąpienia.", "Class_0_incorrectly_extends_base_class_1_2415": "<PERSON><PERSON><PERSON> „{0}” niepoprawnie rozszerza klasę bazową „{1}”.", "Class_0_incorrectly_implements_class_1_Did_you_mean_to_extend_1_and_inherit_its_members_as_a_subclas_2720": "<PERSON><PERSON><PERSON> „{0}” niepoprawnie implementuje klas<PERSON> „{1}”. <PERSON><PERSON> chodziło o rozszerzenie „{1}” i odziedziczenie jego elementów członkowskich jako podklasy?", "Class_0_incorrectly_implements_interface_1_2420": "<PERSON><PERSON><PERSON> „{0}” zawiera niepoprawną implementację interfejsu „{1}”.", "Class_0_used_before_its_declaration_2449": "<PERSON><PERSON><PERSON> „{0}” została użyta przed zadeklarowaniem.", "Class_constructor_may_not_be_a_generator_1368": "Konstruktor klas nie może by<PERSON>.", "Class_constructor_may_not_be_an_accessor_1341": "Konstruktor klas nie może być metodą <PERSON>.", "Class_declaration_cannot_implement_overload_list_for_0_2813": "Deklaracja klasy nie może implementować listy przeciążeń dla elementu \"{0}\".", "Class_declarations_cannot_have_more_than_one_augments_or_extends_tag_8025": "Deklaracje klas nie mogą mieć więcej niż jeden tag \"@augments\" lub \"@extends\".", "Class_decorators_can_t_be_used_with_static_private_identifier_Consider_removing_the_experimental_dec_18036": "Dekoratorów klas nie można używać ze statycznym identyfikatorem prywatnym. Rozważ usunięcie eksperymentalnego dekoratora.", "Class_field_0_defined_by_the_parent_class_is_not_accessible_in_the_child_class_via_super_2855": "Pole klasy „{0}” zdefiniowane przez klasę nadrzędną nie jest dostępne w klasie podrzędnej za pośrednictwem super.", "Class_name_cannot_be_0_2414": "<PERSON>lasa nie może mieć nazwy „{0}”.", "Class_name_cannot_be_Object_when_targeting_ES5_with_module_0_2725": "Nazwą klasy nie może być słowo „Object”, gdy docelowym językiem jest ES5 z modułem {0}.", "Class_static_side_0_incorrectly_extends_base_class_static_side_1_2417": "Strona statyczna klasy „{0}” niepoprawnie rozszerza stronę statyczną klasy bazowej „{1}”.", "Classes_can_only_extend_a_single_class_1174": "Klasy mogą rozszerzać tylko pojedynczą klasę.", "Classes_may_not_have_a_field_named_constructor_18006": "Klasy nie mogą mieć pola o nazwie „constructor”.", "Code_contained_in_a_class_is_evaluated_in_JavaScript_s_strict_mode_which_does_not_allow_this_use_of__1210": "Kod zawarty w klasie jest obliczany w trybie z ograniczeniami języka JavaScript, kt<PERSON>ry nie zezwala na użycie elementu \"{0}\". <PERSON><PERSON> uzy<PERSON> więcej informacji, zobacz https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.", "Command_line_Options_6171": "<PERSON><PERSON><PERSON> w<PERSON>za pole<PERSON>nia", "Compile_the_project_given_the_path_to_its_configuration_file_or_to_a_folder_with_a_tsconfig_json_6020": "Skompiluj projekt z uwzględnieniem ścieżki jego pliku konfiguracji lub folderu z plikiem „tsconfig.json”.", "Compiler_Diagnostics_6251": "Diagnostyka kompilatora", "Compiler_option_0_cannot_be_given_an_empty_string_18051": "<PERSON><PERSON><PERSON> kompilatora „{0}” nie może mieć pustego ciągu.", "Compiler_option_0_expects_an_argument_6044": "Op<PERSON>ja kompilatora „{0}” oczekuje argumentu.", "Compiler_option_0_may_not_be_used_with_build_5094": "<PERSON><PERSON><PERSON> kompilatora \"--{0}\" nie może być używana z parametrem \"--build\".", "Compiler_option_0_may_only_be_used_with_build_5093": "<PERSON><PERSON><PERSON><PERSON> kompilatora \"--{0}\" można używać tylko z parametrem \"--build\".", "Compiler_option_0_of_value_1_is_unstable_Use_nightly_TypeScript_to_silence_this_error_Try_updating_w_4124": "<PERSON><PERSON>ja kompilatora \"{0}\" wartości '{1}' jest niestabilna. Użyj nocnego TypeScript, aby wycis<PERSON>ć ten błąd. Spróbuj zaktualizować za pomocą polecenia „npm install -D typescript@next”.", "Compiler_option_0_requires_a_value_of_type_1_5024": "<PERSON><PERSON><PERSON> kompilatora „{0}” wymaga wartości typu {1}.", "Compiler_reserves_name_0_when_emitting_private_identifier_downlevel_18027": "Kompilator rezerwuje nazwę „{0}” podczas emitowania identyfikatora prywatnego na niższy poziom.", "Compiles_the_TypeScript_project_located_at_the_specified_path_6927": "Kompiluje projekt języka TypeScript znajdujący się w określonej ścieżce.", "Compiles_the_current_project_tsconfig_json_in_the_working_directory_6923": "Kompiluje bieżący projekt (plik tsconfig.json w katalogu roboczym).", "Compiles_the_current_project_with_additional_settings_6929": "Kompiluje bieżący projekt z dodatkowymi ustawieniami.", "Completeness_6257": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Composite_projects_may_not_disable_declaration_emit_6304": "Projekty kompozytowe nie mogą wyłączyć emitowania deklaracji.", "Composite_projects_may_not_disable_incremental_compilation_6379": "Projekty złożone nie mogą wyłączać kompilacji przyrostowej.", "Computed_from_the_list_of_input_files_6911": "Obliczono na podstawie listy plików wejściowych", "Computed_properties_must_be_number_or_string_literals_variables_or_dotted_expressions_with_isolatedD_9014": "Obliczone właściwości muszą być literałami liczb lub ciągów, zmiennymi lub wyrażeniami kropkowanymi z wyrażeniem --isolatedDeclarations.", "Computed_property_names_are_not_allowed_in_enums_1164": "Obliczone nazwy właściwości nie są dozwolone w wyliczeniach.", "Computed_property_names_on_class_or_object_literals_cannot_be_inferred_with_isolatedDeclarations_9038": "Nie można wywnioskować obliczonych nazw właściwości w literałach klas lub obiektów za pomocą wyrażenia --isolatedDeclarations.", "Computed_values_are_not_permitted_in_an_enum_with_string_valued_members_2553": "Obliczone wartości nie są dozwolone w wyliczeniu ze składowymi o wartości ciągu.", "Concatenate_and_emit_output_to_single_file_6001": "Połącz i wyemituj dane wyjściowe do pojedynczego pliku.", "Conditions_to_set_in_addition_to_the_resolver_specific_defaults_when_resolving_imports_6410": "<PERSON><PERSON><PERSON>, które należy ustawić oprócz ustawień domyślnych specyficznych dla programu rozpoznawania nazw podczas rozpoznawania importów.", "Conflicts_are_in_this_file_6201": "Konflikty znajdują się w tym pliku.", "Consider_adding_a_declare_modifier_to_this_class_6506": "Rozważ dodanie modyfikatora \"declare\" do tej klasy.", "Construct_signature_return_types_0_and_1_are_incompatible_2203": "Zwracane typy „{0}” i „{1}” sygnatur konstrukcji są niezgodne.", "Construct_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7013": "Dla sygnatury konstrukcji bez adnotacji zwracanego typu niejawnie określono zwracany typ „any”.", "Construct_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2205": "Sygnatury konstrukcji bez argumentów mają niezgodne zwracane typy „{0}” i „{1}”.", "Constructor_implementation_is_missing_2390": "Brak <PERSON><PERSON>ji k<PERSON>uk<PERSON>.", "Constructor_of_class_0_is_private_and_only_accessible_within_the_class_declaration_2673": "Konstruktor klasy „{0}” jest prywatny i dostępny tylko w ramach deklaracji klasy.", "Constructor_of_class_0_is_protected_and_only_accessible_within_the_class_declaration_2674": "Konstruktor klasy „{0}” jest chroniony i dostępny tylko w ramach deklaracji klasy.", "Constructor_type_notation_must_be_parenthesized_when_used_in_a_union_type_1386": "Notacja typu konstruktora musi być ujęta w nawiasy, je<PERSON>li jest używana w typie unii.", "Constructor_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1388": "Notacja typu konstruktora musi być ujęta w nawiasy, jeśli jest używana w typie przecięcia.", "Constructors_for_derived_classes_must_contain_a_super_call_2377": "Konstruktory klas pochodnych muszą zawierać wywołanie „super”.", "Containing_file_is_not_specified_and_root_directory_cannot_be_determined_skipping_lookup_in_node_mod_6126": "Nie podano pliku zawierającego i nie można określić katalogu głównego. Pomijanie wyszukiwania w folderze „node_modules”.", "Containing_function_is_not_an_arrow_function_95128": "Funkcja zawierająca nie jest funkcją strzałki", "Control_what_method_is_used_to_detect_module_format_JS_files_1475": "<PERSON><PERSON><PERSON><PERSON>, jaka metoda jest używana do wykrywania plików JS w formacie modułu.", "Conversion_of_type_0_to_type_1_may_be_a_mistake_because_neither_type_sufficiently_overlaps_with_the__2352": "Konwersja typu „{0}” na typ „{1}” m<PERSON>ż<PERSON> być błędem, ponieważ żaden z tych typów nie pokrywa się w wystarczającym stopniu z drugim. <PERSON><PERSON><PERSON> by<PERSON>o to zamierzone, najpierw przekonwertuj wyrażenie na typ „unknown”.", "Convert_0_to_1_in_0_95003": "Konwertuj element „{0}” na element „{1} w {0}”.", "Convert_0_to_mapped_object_type_95055": "Konwertuj element „{0}” na zamapowany typ obiektu", "Convert_all_const_to_let_95102": "Konwertuj wszystkie elementy „const” na „let”", "Convert_all_constructor_functions_to_classes_95045": "Przekonwertuj wszystkie funkcje konstruktora na klasy", "Convert_all_invalid_characters_to_HTML_entity_code_95101": "Konwertuj wszystkie nieprawidłowe znaki na kod jednostki HTML", "Convert_all_re_exported_types_to_type_only_exports_1365": "Konwertuj wszystkie ponownie wyeksportowane typy na eksporty dotyczące tylko typu", "Convert_all_require_to_import_95048": "Konwertuj wszystkie wywołania „require” na wywołania „import”", "Convert_all_to_async_functions_95066": "Konwertuj wszystko na funkcje asynchroniczne", "Convert_all_to_bigint_numeric_literals_95092": "Konwertuj wszystko na literały liczbowe typu bigint", "Convert_all_to_default_imports_95035": "Przekonwertuj wszystko na domyślne importowanie", "Convert_all_type_literals_to_mapped_type_95021": "Konwertuj wszystkie literały typu na typ zamapowany", "Convert_all_typedef_to_TypeScript_types_95177": "Konwertuj wszystkie elementy typedef na typy TypeScript.", "Convert_arrow_function_or_function_expression_95122": "Konwertuj funkcję strzałki lub wyrażenie funkcji", "Convert_const_to_let_95093": "Konwertuj zmienne „const” na „let”", "Convert_default_export_to_named_export_95061": "Konwertuj eksport domyślny na nazwany eksport", "Convert_function_declaration_0_to_arrow_function_95106": "Konwertuj deklarac<PERSON> „{0}” na funkcję strzałki", "Convert_function_expression_0_to_arrow_function_95105": "Konwertuj wyrażenie funkcji „{0}” na funkcję strzałki", "Convert_function_to_an_ES2015_class_95001": "Konwertuj funkcję na klasę ES2015", "Convert_invalid_character_to_its_html_entity_code_95100": "Konwertuj nieprawidłowy znak na jego kod jednostki html", "Convert_named_export_to_default_export_95062": "Konwertuj nazwany eksport na eksport domyślny", "Convert_named_imports_to_default_import_95170": "Konwertuj nazwane importy na import domyślny", "Convert_named_imports_to_namespace_import_95057": "Konwertuj importy nazwane na import przestrzeni nazw", "Convert_namespace_import_to_named_imports_95056": "Konwertuj import przestrzeni nazw na importy nazwane", "Convert_overload_list_to_single_signature_95118": "Konwertuj listę przeciążeń na pojedynczą sygnaturę", "Convert_parameters_to_destructured_object_95075": "Konwertuj parametry na obiekt destrukturyzowany", "Convert_require_to_import_95047": "Konwertuj wywołanie „require” na wywołanie „import”", "Convert_to_ES_module_95017": "Konwertuj na moduł ES", "Convert_to_a_bigint_numeric_literal_95091": "Konwertuj na literał liczbowy typu bigint", "Convert_to_anonymous_function_95123": "Konwertuj na funkcję anonimową", "Convert_to_arrow_function_95125": "Konwertuj na funkcję strzałki", "Convert_to_async_function_95065": "Konwertuj na funkcję asynchroniczną", "Convert_to_default_import_95013": "Konwertuj na import domyślny", "Convert_to_named_function_95124": "Konwertuj na funkcję nazwaną", "Convert_to_optional_chain_expression_95139": "Konwertuj na opcjonalne wyrażenie łańcucha", "Convert_to_template_string_95096": "Konwertuj na ciąg szablonu", "Convert_to_type_only_export_1364": "Konwertuj na eksport dotyczący tylko typu", "Convert_typedef_to_TypeScript_type_95176": "Konwertuj element typedef na typ TypeScript.", "Corrupted_locale_file_0_6051": "Uszkodzony plik ustawień regionalnych {0}.", "Could_not_convert_to_anonymous_function_95153": "Nie można przekonwertować na funkcję anonimową", "Could_not_convert_to_arrow_function_95151": "Nie można przekonwertować na funkcję strzałki", "Could_not_convert_to_named_function_95152": "Nie można przekonwertować na nazwaną funkcję", "Could_not_determine_function_return_type_95150": "<PERSON>e moż<PERSON> zwracanego typu funkcji", "Could_not_find_a_containing_arrow_function_95127": "Nie można było z<PERSON> zawierającej funkcji strzałki", "Could_not_find_a_declaration_file_for_module_0_1_implicitly_has_an_any_type_7016": "Nie można znaleźć pliku deklaracji dla modułu „{0}”. Element „{1}” ma niejawnie typ „any”.", "Could_not_find_convertible_access_expression_95140": "Nie można odnaleźć konwertowalnego wyrażenia dostępu", "Could_not_find_export_statement_95129": "Nie można było z<PERSON> instrukcji export", "Could_not_find_import_clause_95131": "Nie można było znaleźć klauzuli import", "Could_not_find_matching_access_expressions_95141": "Nie można odnaleźć zgodnych wyrażeń dostępu", "Could_not_find_name_0_Did_you_mean_1_2570": "Nie można znaleźć nazwy „{0}”. <PERSON><PERSON> o „{1}”?", "Could_not_find_namespace_import_or_named_imports_95132": "Nie można było z<PERSON> importu przestrzeni nazw lub nazwanych importów", "Could_not_find_property_for_which_to_generate_accessor_95135": "<PERSON>e można był<PERSON> w<PERSON>, dla której ma zostać wygenerowana metoda dostępu", "Could_not_find_variable_to_inline_95185": "Nie można odnaleźć zmiennej w tekście.", "Could_not_resolve_the_path_0_with_the_extensions_Colon_1_6231": "<PERSON>e można rozpoznać ścieżki „{0}” z rozszerzeniami: {1}.", "Could_not_write_file_0_Colon_1_5033": "<PERSON><PERSON> można zap<PERSON>ć pliku „{0}”: {1}.", "Create_source_map_files_for_emitted_JavaScript_files_6694": "Twórz pliki mapy źródła dla emitowanych plików JavaScript.", "Create_sourcemaps_for_d_ts_files_6614": "Twórz mapy źródła dla plików d.ts.", "Creates_a_tsconfig_json_with_the_recommended_settings_in_the_working_directory_6926": "Tworzy plik tsconfig.json z zalecanymi ustawieniami w katalogu roboczym.", "DIRECTORY_6038": "KATALOG", "Decimal_escape_sequences_and_backreferences_are_not_allowed_in_a_character_class_1537": "Sekwencje ucieczki i odwołania wsteczne dziesiętne nie są dozwolone w klasie znaków.", "Decimals_with_leading_zeros_are_not_allowed_1489": "Wartości dziesiętne z zerami wiodącymi są niedozwolone.", "Declaration_augments_declaration_in_another_file_This_cannot_be_serialized_6232": "Deklaracja rozszerza deklarację w innym pliku. Nie można przeprowadzić serializacji.", "Declaration_emit_for_this_file_requires_preserving_this_import_for_augmentations_This_is_not_support_9026": "<PERSON><PERSON>ja deklaracji dla tego pliku wymaga zachowania tego importu dla rozszerzeń. Nie jest to obsługiwane w przypadku parametru --isolatedDeclarations.", "Declaration_emit_for_this_file_requires_using_private_name_0_An_explicit_type_annotation_may_unblock_9005": "Emitowanie deklaracji dla tego pliku wymaga użycia nazwy prywatnej „{0}”. <PERSON><PERSON><PERSON> adnotacja typu może odblokować emitowanie deklaracji.", "Declaration_emit_for_this_file_requires_using_private_name_0_from_module_1_An_explicit_type_annotati_9006": "Emitowanie deklaracji dla tego pliku wymaga użycia nazwy prywatnej „{0}” z modułu „{1}”. Jawna adnotacja typu może odblokować emitowanie deklaracji.", "Declaration_emit_for_this_parameter_requires_implicitly_adding_undefined_to_it_s_type_This_is_not_su_9025": "<PERSON><PERSON><PERSON> deklaracji dla tego parametru wymaga niejawnego dodania niezdefiniowanego do jego typu. Nie jest to obsługiwane w przypadku parametru --isolatedDeclarations.", "Declaration_expected_1146": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "Declaration_name_conflicts_with_built_in_global_identifier_0_2397": "Nazwa deklaracji powoduje konflikt z wbudowanym identyfikatorem globalnym „{0}”.", "Declaration_or_statement_expected_1128": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lub instrukcji.", "Declaration_or_statement_expected_This_follows_a_block_of_statements_so_if_you_intended_to_write_a_d_2809": "Oczekiwano deklaracji lub instrukcji. Ten znak „=” jest za blokiem instrukcji, dlatego jeśli chodziło Ci o napisanie przypisania usuwającego, może być konieczne zamknięcie całego przypisania w nawiasach.", "Declarations_with_definite_assignment_assertions_must_also_have_type_annotations_1264": "Deklaracje z asercjami określonego przypisania muszą również mieć adnotacje typu.", "Declarations_with_initializers_cannot_also_have_definite_assignment_assertions_1263": "Deklaracje z inicjatorami nie mogą mieć również asercji określonego przypisania.", "Declare_a_private_field_named_0_90053": "Zadeklaruj pole prywatne o nazwie „{0}”.", "Declare_method_0_90023": "Zadekla<PERSON><PERSON> „{0}”", "Declare_private_method_0_90038": "Zadeklaruj met<PERSON><PERSON> p<PERSON> „{0}”", "Declare_private_property_0_90035": "Zadeklaruj wła<PERSON><PERSON><PERSON><PERSON>ć prywatną „{0}”", "Declare_property_0_90016": "Zadeklaruj <PERSON>ć „{0}”", "Declare_static_method_0_90024": "Zadeklaruj metodę statyczną „{0}”", "Declare_static_property_0_90027": "Zadeklaruj właściwość statyczną „{0}”", "Decorator_function_return_type_0_is_not_assignable_to_type_1_1270": "Zwracany typ funkcji dekoratora „{0}” nie jest możliwy do przypisania do typu „{1}”.", "Decorator_function_return_type_is_0_but_is_expected_to_be_void_or_any_1271": "Zwracany typ <PERSON><PERSON><PERSON> de<PERSON> to „{0}”, natomiast p<PERSON><PERSON><PERSON> by<PERSON> to typ „void” lub „any”.", "Decorator_used_before_export_here_1486": "Dekorator używany przed elementem „export” w tym miejscu.", "Decorators_are_not_valid_here_1206": "Elementy Decorator nie są tutaj prawidłowe.", "Decorators_cannot_be_applied_to_multiple_get_Slashset_accessors_of_the_same_name_1207": "Nie można stosować elementów Decorator do wielu metod dostępu pobierania/ustawiania o takiej samej nazwie.", "Decorators_may_not_appear_after_export_or_export_default_if_they_also_appear_before_export_8038": "Dekoratory nie mogą występować po instrukcji „export” lub „export default”, jeśli występują również przed instrukcją „export”.", "Decorators_must_precede_the_name_and_all_keywords_of_property_declarations_1436": "Dekoratory muszą poprzedzać nazwę i wszystkie słowa kluczowe deklaracji właściwości.", "Default_catch_clause_variables_as_unknown_instead_of_any_6803": "<PERSON><PERSON>z zmienne klauzuli catch jako „unknown” zamiast „any”.", "Default_export_of_the_module_has_or_is_using_private_name_0_4082": "Domyślny eksport modułu ma nazwę prywatną „{0}” lub używa tej nazwy.", "Default_exports_can_t_be_inferred_with_isolatedDeclarations_9037": "Eksportów domyślnych nie można wywnioskować za pomocą wyrażenia --isolatedDeclarations.", "Default_library_1424": "Domyślna biblioteka", "Default_library_for_target_0_1425": "Domyślna biblioteka dla elementu docelowego „{0}”", "Definitions_of_the_following_identifiers_conflict_with_those_in_another_file_Colon_0_6200": "Definicje następujących identyfikatorów powodują konflikt z tymi w innym pliku: {0}", "Delete_all_unused_declarations_95024": "Usuń wszystkie nieużywane deklaracje", "Delete_all_unused_imports_95147": "Usuń wszystkie nieużywane importy", "Delete_all_unused_param_tags_95172": "Usuń wszystkie nieużywane tagi „@param”", "Delete_the_outputs_of_all_projects_6365": "Usuń dane wyjściowe wszystkich projektów.", "Delete_unused_param_tag_0_95171": "Usuń nieużywany tag „@param” „{0}”", "Deprecated_Use_jsxFactory_instead_Specify_the_object_invoked_for_createElement_when_targeting_react__6084": "[Przestarzałe] Użyj w zastępstwie opcji „--jsxFactory”. Określ obiekt wywoływany dla elementu createElement przy określaniu jako celu emisji JSX „react”", "Deprecated_Use_outFile_instead_Concatenate_and_emit_output_to_single_file_6170": "[Prz<PERSON>rz<PERSON>ł<PERSON>] Użyj w zastępstwie opcji „--outFile”. Połącz dane wyjściowe i wyemituj jako jeden plik", "Deprecated_Use_skipLibCheck_instead_Skip_type_checking_of_default_library_declaration_files_6160": "[Prz<PERSON>rz<PERSON>ł<PERSON>] Użyj w zastępstwie opcji „--skipLibCheck”. Pomiń sprawdzanie typów domyślnych plików deklaracji biblioteki.", "Deprecated_setting_Use_outFile_instead_6677": "Przestarzałe ustawienie. Zamiast tego użyj elementu „outFile”.", "Did_you_forget_to_use_await_2773": "<PERSON><PERSON> zapomniano użyć operatora „await”?", "Did_you_mean_0_1369": "<PERSON><PERSON> ch<PERSON>i o „{0}”?", "Did_you_mean_for_0_to_be_constrained_to_type_new_args_Colon_any_1_2735": "<PERSON><PERSON> chodziło Ci o ograniczenie elementu „{0}” do typu „new (...args: any[]) => {1}”?", "Did_you_mean_to_call_this_expression_6212": "<PERSON><PERSON> chodziło Ci o wywołanie tego wyrażenia?", "Did_you_mean_to_mark_this_function_as_async_1356": "<PERSON><PERSON> chodziło Ci o oznaczenie tej funkcji jako „async”?", "Did_you_mean_to_use_a_Colon_An_can_only_follow_a_property_name_when_the_containing_object_literal_is_1312": "<PERSON><PERSON> chodziło o użycie znaku „:”? Znak „=” może występować po nazwie właściwości tylko wtedy, gdy zawieraj<PERSON>cy literał obiektu jest częścią wzorca usuwającego strukturę.", "Did_you_mean_to_use_new_with_this_expression_6213": "<PERSON>zy chodziło Ci o użycie operatora „new” z tym wyrażeniem?", "Digit_expected_1124": "Oczekiwano cyfry.", "Directory_0_does_not_exist_skipping_all_lookups_in_it_6148": "Katalog „{0}” nie istnieje. Operacje wyszukiwania w nim zostaną pominięte.", "Directory_0_has_no_containing_package_json_scope_Imports_will_not_resolve_6270": "Katalog „{0}” nie zawiera zakresu package.json. Importy nie zostaną rozpoznane.", "Disable_adding_use_strict_directives_in_emitted_JavaScript_files_6669": "Wyłącz dodawanie dyrektyw „use strict” w emitowanych plikach JavaScript.", "Disable_checking_for_this_file_90018": "Wyłącz sprawdzanie dla tego pliku", "Disable_emitting_comments_6688": "Wyłącz emitowanie komentarzy.", "Disable_emitting_declarations_that_have_internal_in_their_JSDoc_comments_6701": "Wyłącz emitowanie deklaracji mających element „@internal” w komentarzach JSDoc.", "Disable_emitting_files_from_a_compilation_6660": "Wyłącz emitowanie plików z kompilacji.", "Disable_emitting_files_if_any_type_checking_errors_are_reported_6662": "Wyłącz emitowanie plików, jeś<PERSON> zostaną zgłoszone jakiekolwiek błędy sprawdzania typów.", "Disable_erasing_const_enum_declarations_in_generated_code_6682": "Wyłącz wymazywanie deklaracji „const enum” w wygenerowanym kodzie.", "Disable_error_reporting_for_unreachable_code_6603": "Wyłącz raportowanie błędów dla nieosiągalnego kodu.", "Disable_error_reporting_for_unused_labels_6604": "Wyłącz raportowanie błędów dla nieużywanych etykiet.", "Disable_full_type_checking_only_critical_parse_and_emit_errors_will_be_reported_6805": "Wyłącz sprawdzanie pełnego typu (będą zgłaszane tylko krytyczne analizy i błędy emisji).", "Disable_generating_custom_helper_functions_like_extends_in_compiled_output_6661": "Wyłącz generowanie niestandardowych funkcji pomocniczych, takich jak „__extends” w skompilowanych danych wyjściowych.", "Disable_including_any_library_files_including_the_default_lib_d_ts_6670": "Wyłącz dołączanie plików bibliotek, w tym domyślnego pliku lib.d.ts.", "Disable_loading_referenced_projects_6235": "Wyłącz ładowanie przywoływanych projektów.", "Disable_preferring_source_files_instead_of_declaration_files_when_referencing_composite_projects_6620": "Wyłącz preferowanie plików źródłowych zamiast plików deklaracji podczas odwoływania się do projektów złożonych.", "Disable_reporting_of_excess_property_errors_during_the_creation_of_object_literals_6702": "Wyłącz raportowanie błędów dotyczących nadmiarowych właściwości podczas tworzenia literałów obiektów.", "Disable_resolving_symlinks_to_their_realpath_This_correlates_to_the_same_flag_in_node_6683": "Wyłącz rozpoznawanie linków symbolicznych jako ścieżek rzeczywistych. Odpowiada to takiej samej fladze na platformie Node.", "Disable_size_limitations_on_JavaScript_projects_6162": "Wyłącz ograniczenia rozmiarów dla projektów JavaScript.", "Disable_solution_searching_for_this_project_6224": "Wyłącz wyszukiwanie rozwiązania dla tego projektu.", "Disable_strict_checking_of_generic_signatures_in_function_types_6673": "Wyłącz dokładne sprawdzanie sygnatur ogólnych w typach funkcji.", "Disable_the_type_acquisition_for_JavaScript_projects_6625": "Wyłącz pozyskiwanie typów dla projektów JavaScript", "Disable_truncating_types_in_error_messages_6663": "Wyłącz obcinanie typów w komunikatach o błędach.", "Disable_use_of_source_files_instead_of_declaration_files_from_referenced_projects_6221": "Wyłącz używanie plików źródłowych zamiast plików deklaracji z przywoływanych projektów.", "Disable_wiping_the_console_in_watch_mode_6684": "Wyłącz czyszczenie konsoli w trybie obserwacji.", "Disables_inference_for_type_acquisition_by_looking_at_filenames_in_a_project_6616": "Wyłącz wnioskowanie dla pozyskiwania typów przez sprawdzanie nazw plików w projekcie.", "Disallow_import_s_require_s_or_reference_s_from_expanding_the_number_of_files_TypeScript_should_add__6672": "Nie zezwalaj elementom „import”, „require” i „<reference>” na zwiększanie liczby plików, które powinny zostać dodane do projektu przez język TypeScript.", "Disallow_inconsistently_cased_references_to_the_same_file_6078": "Nie zezwalaj na przywoływanie tego samego pliku za pomocą nazw różniących się wielkością liter.", "Do_not_add_triple_slash_references_or_imported_modules_to_the_list_of_compiled_files_6159": "Nie dodawaj odwołań z trzema ukośnikami ani zaimportowanych modułów do listy skompilowanych plików.", "Do_not_emit_comments_to_output_6009": "Nie emituj komentarzy do danych wyjściowych.", "Do_not_emit_declarations_for_code_that_has_an_internal_annotation_6056": "<PERSON>e emitu<PERSON> deklaracji dla kodu z adnotacją „@internal”.", "Do_not_emit_outputs_6010": "<PERSON>e emituj danych wyjściowych.", "Do_not_emit_outputs_if_any_errors_were_reported_6008": "Nie emituj danych wyjściowych w przypadku zgłoszenia błędów.", "Do_not_emit_use_strict_directives_in_module_output_6112": "<PERSON>e emituj d<PERSON> „use strict” w danych wyjściowych modułu.", "Do_not_erase_const_enum_declarations_in_generated_code_6007": "Nie wymazuj deklaracji wyliczeń ze specyfikacją const w wygenerowanym kodzie.", "Do_not_generate_custom_helper_functions_like_extends_in_compiled_output_6157": "Nie generuj w skompilowanych danych wyjściowych niestandardowych funkcji pomocy, takich jak „__extends”.", "Do_not_include_the_default_library_file_lib_d_ts_6158": "Nie uwzględniaj domyślnego pliku biblioteki (lib.d.ts).", "Do_not_report_errors_on_unreachable_code_6077": "Nie zgła<PERSON>aj błędów dla nieosiągalnego kodu.", "Do_not_report_errors_on_unused_labels_6074": "Nie zgła<PERSON><PERSON> błędów dla nieużywanych etykiet.", "Do_not_resolve_the_real_path_of_symlinks_6013": "Nie rozpoznawaj rzeczywistej ścieżki linków symbolicznych.", "Do_not_transform_or_elide_any_imports_or_exports_not_marked_as_type_only_ensuring_they_are_written_i_6804": "Nie przekształcaj ani nie usuwaj żadnych importów ani eksportów, które nie są oznaczone jako tylko typy, d<PERSON><PERSON><PERSON> czemu są zapisywane w formacie pliku wyjściowego na podstawie ustawienia „module”.", "Do_not_truncate_error_messages_6165": "Nie obcinaj komunikatów o błędach.", "Duplicate_function_implementation_2393": "Zduplikowana implementac<PERSON>.", "Duplicate_identifier_0_2300": "Zduplikowany identyfikator „{0}”.", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_2441": "Zduplikowany identyfikator „{0}”. Kompilator rezerwuje nazwę „{1}” w zakresie najwyższego poziomu modułu.", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_containing_async_func_2529": "Zduplikowany identyfikator „{0}”. Kompilator rezerwuje nazwę „{1}” w zakresie najwyższego poziomu modułu zawierającego funkcje asynchroniczne.", "Duplicate_identifier_0_Compiler_reserves_name_1_when_emitting_super_references_in_static_initializer_2818": "Zduplikowany identyfikator \"{0}\". Kompilator rezerwuje nazwę \"{1}\" podczas emisji odwołań \"super\" w inicjatorach statycznych.", "Duplicate_identifier_0_Compiler_uses_declaration_1_to_support_async_functions_2520": "Zduplikowany identyfikator „{0}”. Kompilator używa deklaracji „{1}” do obsługi funkcji asynchronicznych.", "Duplicate_identifier_0_Static_and_instance_elements_cannot_share_the_same_private_name_2804": "Zduplikowany identyfikator „{0}”. Elementy statyczne i elementy wystąpienia nie mogą mieć tej samej nazwy prywatnej.", "Duplicate_identifier_arguments_Compiler_uses_arguments_to_initialize_rest_parameters_2396": "Zduplikowany identyfikator „arguments”. Kompilator używa ciągu „arguments” do zainicjowania parametrów rest.", "Duplicate_identifier_newTarget_Compiler_uses_variable_declaration_newTarget_to_capture_new_target_me_2543": "Powielony identyfikator „_newTarget”. Kompilator używa deklaracji zmiennej „_newTarget” do przechwytywania odwołania do metawłaściwości „new.target”.", "Duplicate_identifier_this_Compiler_uses_variable_declaration_this_to_capture_this_reference_2399": "Zduplikowany identyfikator „_this”. Kompilator używa deklaracji zmiennej „_this” do przechwycenia odwołania do elementu „this”.", "Duplicate_index_signature_for_type_0_2374": "Zduplikowana sygnatura indeksu dla typu „{0}”.", "Duplicate_label_0_1114": "Zduplikowana etykieta „{0}”.", "Duplicate_property_0_2718": "Zduplikowana właś<PERSON><PERSON>ść „{0}”.", "Duplicate_regular_expression_flag_1500": "Zduplikowana flaga wyrażenia regularnego.", "Dynamic_import_s_specifier_must_be_of_type_string_but_here_has_type_0_7036": "Specyfikator dynamicznego importowania musi być typu „string”, ale określono typ „{0}”.", "Dynamic_imports_are_only_supported_when_the_module_flag_is_set_to_es2020_es2022_esnext_commonjs_amd__1323": "Dynamiczne importy są obsługiwane tylko wtedy, gdy <PERSON>a „--module” jest ustawiona na „es2020”, „es2022”, „esnext”, „commonjs”, „amd”, „system”, „umd”, „node16” lub „nodenext”.", "Dynamic_imports_can_only_accept_a_module_specifier_and_an_optional_set_of_attributes_as_arguments_1450": "Importy dynamiczne mogą akceptować tylko specyfikator modułu i opcjonalny zestaw atrybutów jako argumenty", "Dynamic_imports_only_support_a_second_argument_when_the_module_option_is_set_to_esnext_node16_nodene_1324": "Importy dynamiczne obsługują tylko drugi argument, gdy opcja „--module” jest ustawiona na wartość „esnext”, „node16”, „nodenext” lub „preserve”.", "ESM_syntax_is_not_allowed_in_a_CommonJS_module_when_module_is_set_to_preserve_1293": "Składnia ESM jest niedozwolona w module CommonJS, gdy element „module” jest ustawiony na wartość „preserve”.", "ESM_syntax_is_not_allowed_in_a_CommonJS_module_when_verbatimModuleSyntax_is_enabled_1286": "Składnia ESM jest niedozwolona w module CommonJS, gdy jest włączona opcja „verbatimModuleSyntax”.", "Each_declaration_of_0_1_differs_in_its_value_where_2_was_expected_but_3_was_given_4125": "Każda deklaracja znaku „{0}.{1}” różni się wartością, gdzie oczekiwano elementu „{2}”, ale podano „{3}”.", "Each_member_of_the_union_type_0_has_construct_signatures_but_none_of_those_signatures_are_compatible_2762": "Każdy element członkowski typu unii „{0}” ma sygnatury konstrukcji, ale żadne z tych sygnatur nie są ze sobą zgodne.", "Each_member_of_the_union_type_0_has_signatures_but_none_of_those_signatures_are_compatible_with_each_2758": "Każdy element członkowski typu unii „{0}” ma sygnatury, ale żadne z tych sygnatur nie są ze sobą zgodne.", "Editor_Support_6249": "Pomoc techniczna edytora", "Element_implicitly_has_an_any_type_because_expression_of_type_0_can_t_be_used_to_index_type_1_7053": "Element ma niejawnie typ „any”, ponieważ wyrażenie typu „{0}” nie może być używane do indeksowania typu „{1}”.", "Element_implicitly_has_an_any_type_because_index_expression_is_not_of_type_number_7015": "Element niejawnie przyjmuje typ „any”, ponieważ wyrażenie indeksu ma typ inny niż „number”.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_7017": "Element ma niejawnie typ „any”, ponieważ typ „{0}” nie ma sygnatury indeksu.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_Did_you_mean_to_call_1_7052": "Element ma niejawnie typ „any”, ponieważ typ „{0}” nie ma sygnatury indeksu. <PERSON><PERSON> chodziło o wywołanie „{1}”?", "Emit_6246": "<PERSON><PERSON><PERSON><PERSON>", "Emit_ECMAScript_standard_compliant_class_fields_6712": "Emituj pola klasy zgodne ze standardem ECMAScript.", "Emit_a_UTF_8_Byte_Order_Mark_BOM_in_the_beginning_of_output_files_6622": "Emituj znacznik kolejności bajtów UTF-8 na początku plików wyjściowych.", "Emit_a_single_file_with_source_maps_instead_of_having_a_separate_file_6151": "Emituj pojedynczy plik z mapami źródeł zamiast oddzielnego pliku.", "Emit_a_v8_CPU_profile_of_the_compiler_run_for_debugging_6638": "Emituj profil procesora CPU v8 dla uruchomienia kompilatora na potrzeby debugowania.", "Emit_additional_JavaScript_to_ease_support_for_importing_CommonJS_modules_This_enables_allowSyntheti_6626": "Emituj dodatkowy kod JavaScript, aby ułatwić obsługę importowania modułów CommonJS. Włącza to opcję „allowSyntheticDefaultImports” na potrzeby zgodności typów.", "Emit_class_fields_with_Define_instead_of_Set_6222": "Emituj pola klasy przy użyciu metody Define zamiast Set.", "Emit_design_type_metadata_for_decorated_declarations_in_source_files_6624": "Emituj metadane typu „design” dla dekorowanych deklaracji w plikach źródłowych.", "Emit_more_compliant_but_verbose_and_less_performant_JavaScript_for_iteration_6621": "<PERSON>it<PERSON>j bard<PERSON>j <PERSON>godn<PERSON>, ale dosłowny i mniej wydajny JavaScript dla iteracji.", "Emit_the_source_alongside_the_sourcemaps_within_a_single_file_requires_inlineSourceMap_or_sourceMap__6152": "Emituj źródło razem z mapami źródeł w pojedynczym pliku; wymaga ustawienia opcji „--inlineSourceMap” lub „--sourceMap”.", "Enable_all_strict_type_checking_options_6180": "Włącz wszystkie opcje ścisłego sprawdzania typów.", "Enable_color_and_formatting_in_TypeScript_s_output_to_make_compiler_errors_easier_to_read_6685": "Włącz kolor i formatowanie w danych wyjściowych języka TypeScript, aby ułatwić odczytywanie błędów kompilatora.", "Enable_constraints_that_allow_a_TypeScript_project_to_be_used_with_project_references_6611": "Włącz ograniczenia zezwalające na używanie projektu języka TypeScript z odwołaniami do projektów.", "Enable_error_reporting_for_codepaths_that_do_not_explicitly_return_in_a_function_6667": "Włącz raportowanie błędów dla ścieżek kodu, które nie zwracają jawnie funkcji.", "Enable_error_reporting_for_expressions_and_declarations_with_an_implied_any_type_6665": "Włącz raportowanie błędów dla wyrażeń i deklaracji z dorozumianym typem „any”.", "Enable_error_reporting_for_fallthrough_cases_in_switch_statements_6664": "Włącz raportowanie błędów dla przypadków rezerwowych w instrukcjach switch.", "Enable_error_reporting_in_type_checked_JavaScript_files_6609": "Włącz raportowanie błędów w plikach JavaScript z zaznaczonym typem.", "Enable_error_reporting_when_local_variables_aren_t_read_6675": "Włącz raportowanie błędów, gdy zmienne lokalne nie są odczytywane.", "Enable_error_reporting_when_this_is_given_the_type_any_6668": "Włącz raportowanie błędów, gdy element „this” ma typ „any”.", "Enable_experimental_support_for_legacy_experimental_decorators_6630": "Włącz eksperymentalną obsługę starszych dekoratorów eksperymentalnych.", "Enable_importing_files_with_any_extension_provided_a_declaration_file_is_present_6264": "Włącz importowanie plików z dowolnym rozszerzeniem, pod warunkiem, że istnieje plik deklaracji.", "Enable_importing_json_files_6689": "Włącz importowanie plików json.", "Enable_project_compilation_6302": "Włącz kompilację projektu", "Enable_strict_bind_call_and_apply_methods_on_functions_6214": "<PERSON>ł<PERSON><PERSON> ścisłe metody „bind”, „call” i „apply” dla funk<PERSON>.", "Enable_strict_checking_of_function_types_6186": "Włącz dokładne sprawdzanie typów funkcji.", "Enable_strict_checking_of_property_initialization_in_classes_6187": "Włącz dokładne sprawdzanie inicjowania właściwości w klasach.", "Enable_strict_null_checks_6113": "Włącz dokładne sprawdzanie wartości null.", "Enable_the_experimentalDecorators_option_in_your_configuration_file_95074": "<PERSON>ł<PERSON><PERSON> opcję „experimentalDecorators” w pliku konfiguracji", "Enable_the_jsx_flag_in_your_configuration_file_95088": "<PERSON>ł<PERSON><PERSON> flagę „--jsx” w pliku konfiguracji", "Enable_tracing_of_the_name_resolution_process_6085": "Włącz śledzenie procesu rozpoznawania nazw.", "Enable_verbose_logging_6713": "Włącz pełne rejestrowanie.", "Enables_emit_interoperability_between_CommonJS_and_ES_Modules_via_creation_of_namespace_objects_for__7037": "Umożliwia współdziałanie emitowania między modułami CommonJS i ES przez tworzenie obiektów przestrzeni nazw dla wszystkich importów. Implikuje użycie ustawienia „allowSyntheticDefaultImports”.", "Enables_experimental_support_for_ES7_decorators_6065": "Umożliwia obsługę eksperymentalną elementów Decorator języka ES7.", "Enables_experimental_support_for_emitting_type_metadata_for_decorators_6066": "Umożliwia obsługę eksperymentalną emitowania metadanych typów elementów Decorator.", "Enforces_using_indexed_accessors_for_keys_declared_using_an_indexed_type_6671": "Wymuszaj używanie indeksowanych metod dostępu dla kluczy deklarowanych przy użyciu typu indeksowanego.", "Ensure_overriding_members_in_derived_classes_are_marked_with_an_override_modifier_6666": "Upewnij <PERSON>ę, że przesłaniające elementy członkowskie w klasach pochodnych są oznaczone modyfikatorem „override”.", "Ensure_that_casing_is_correct_in_imports_6637": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> wielko<PERSON> liter jest poprawna w importach.", "Ensure_that_each_file_can_be_safely_transpiled_without_relying_on_other_imports_6645": "Upewnij się, że każdy plik może być bezpiecznie transponowany bez polegania na innych importach.", "Ensure_use_strict_is_always_emitted_6605": "Upew<PERSON>j się, że element „use strict” jest zawsze emitowany.", "Entering_conditional_exports_6413": "Wprowadzanie eksportów warunkowych.", "Entry_point_for_implicit_type_library_0_1420": "Punkt wejścia dla biblioteki niejawnych typów „{0}”", "Entry_point_for_implicit_type_library_0_with_packageId_1_1421": "Punkt wejścia dla biblioteki niejawnych typów „{0}” o identyfikatorze packageId „{1}”", "Entry_point_of_type_library_0_specified_in_compilerOptions_1417": "Punkt wejścia biblioteki typów „{0}” określony w opcjach compilerOptions", "Entry_point_of_type_library_0_specified_in_compilerOptions_with_packageId_1_1418": "Punkt wejścia biblioteki typów „{0}” określony w opcjach compilerOptions o identyfikatorze packageId „{1}”", "Enum_0_used_before_its_declaration_2450": "Wyliczenie „{0}” zostało użyte przed zadeklarowaniem.", "Enum_declarations_can_only_merge_with_namespace_or_other_enum_declarations_2567": "Deklaracje wyliczeń można scalać tylko z przestrzeniami nazw lub innymi deklaracjami wyliczeń.", "Enum_declarations_must_all_be_const_or_non_const_2473": "Wszystkie deklaracje wyliczeń muszą być elementami const lub żadna nie może być elementem const.", "Enum_member_expected_1132": "Oczekiwano składowych wyliczenia.", "Enum_member_following_a_non_literal_numeric_member_must_have_an_initializer_when_isolatedModules_is__18056": "Składowa wyliczenia następująca po składowej liczbowej niebędącej literałem musi mieć inicjator, gdy jest włączona opcja „isolatedModules”.", "Enum_member_initializers_must_be_computable_without_references_to_external_symbols_with_isolatedDecl_9020": "Inicjatory składowych wyliczenia muszą być możliwe do obliczenia bez odwołań do symboli zewnętrznych z wyrażeniem --isolatedDeclarations.", "Enum_member_must_have_initializer_1061": "Składowa wyliczenia musi mieć inicjator.", "Enum_name_cannot_be_0_2431": "Wyliczenie nie może mieć nazwy „{0}”.", "Errors_Files_6041": "Pliki z błędami", "Escape_sequence_0_is_not_allowed_1488": "Sekwencja ucieczki „{0}” jest niedozwolona.", "Examples_Colon_0_6026": "Przykłady: {0}", "Excessive_complexity_comparing_types_0_and_1_2859": "Nadmierna złożoność podczas porównywania typów „{0}” i „{1}”.", "Excessive_stack_depth_comparing_types_0_and_1_2321": "Nadmierna głębokość stosu podczas porównywania typów „{0}” i „{1}”.", "Exiting_conditional_exports_6416": "Wyjście z eksportu warunkowego.", "Expected_0_1_type_arguments_provide_these_with_an_extends_tag_8027": "Oczekiwano argumentów typu {0}-{1}; podaj je z tagiem „@extends”.", "Expected_0_arguments_but_got_1_2554": "Oczekiwane argumenty: {0}, uzyskano: {1}.", "Expected_0_arguments_but_got_1_Did_you_forget_to_include_void_in_your_type_argument_to_Promise_2794": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {0} <PERSON><PERSON>, ale otrzymano {1}. <PERSON><PERSON> wartość „void” w argumencie typu obiektu „Promise”?", "Expected_0_type_arguments_but_got_1_2558": "Oczekiwane argumenty typu: {0}, uzyskano: {1}.", "Expected_0_type_arguments_provide_these_with_an_extends_tag_8026": "Oczekiwano argumentów typu {0}; podaj je z tagiem „@extends”.", "Expected_1_argument_but_got_0_new_Promise_needs_a_JSDoc_hint_to_produce_a_resolve_that_can_be_called_2810": "Oczekiwano 1 argumentu, ale otrzymano 0. Element „new Promise()” wymaga wskazówki JSDoc, aby ut<PERSON><PERSON><PERSON>ć element „resolve”, który można wywołać bez argumentów.", "Expected_a_Unicode_property_name_1523": "Oczekiwano nazwy właściwości standardu Unicode.", "Expected_a_Unicode_property_name_or_value_1527": "Oczekiwano nazwy lub wartości właściwości standardu Unicode.", "Expected_a_Unicode_property_value_1525": "Oczekiwano wartości właściwości standardu Unicode.", "Expected_a_capturing_group_name_1514": "Oczekiwano nazwy grupy przechwytywania.", "Expected_a_class_set_operand_1520": "Oczekiwano operandu zestawu klas.", "Expected_at_least_0_arguments_but_got_1_2555": "Oczekiwane argumenty: co najmniej {0}, uzyskano: {1}.", "Expected_corresponding_JSX_closing_tag_for_0_17002": "Oczekiwano odpowiadającego tagu zamykającego kodu JSX dla elementu „{0}”.", "Expected_corresponding_closing_tag_for_JSX_fragment_17015": "Oczekiwano odpowiedniego tagu zamykającego dla fragmentu kodu JSX.", "Expected_for_property_initializer_1442": "Oczekiwano znaku „=” dla inicjatora właściwości.", "Expected_type_of_0_field_in_package_json_to_be_1_got_2_6105": "Oczekiwany typ pola „{0}” w pliku „package.json” to „{1}”, a uzyskano typ „{2}”.", "Explicitly_specified_module_resolution_kind_Colon_0_6087": "Jawnie określony rodzaj rozpoznawania modułów: „{0}”.", "Exponentiation_cannot_be_performed_on_bigint_values_unless_the_target_option_is_set_to_es2016_or_lat_2791": "Nie można wykonać potęgowania wartości typu „bigint”, chyba że opcja „target” ma wartość „es2016” lub nowsz<PERSON>.", "Export_0_from_module_1_90059": "Eksportuj „{0}” z modułu „{1}”", "Export_all_referenced_locals_90060": "Eksportuj wszystkie przywoływane zmienne lokalne", "Export_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_export_default_or__1203": "Nie można użyć przypisania eksportu, gdy są używane moduły języka ECMAScript. Zamiast tego rozważ użycie elementu „export default” lub innego formatu modułu.", "Export_assignment_is_not_supported_when_module_flag_is_system_1218": "Przypisanie eksportu nie jest obsługiwane, gdy <PERSON>a „--module” ma postać „system”.", "Export_declaration_conflicts_with_exported_declaration_of_0_2484": "Deklaracja eksportu powoduje konflikt z wyeksportowaną deklaracją „{0}”.", "Export_declarations_are_not_permitted_in_a_namespace_1194": "Deklaracje eksportu są niedozwolone w przestrzeni nazw.", "Export_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6276": "Specyfikator eksportu „{0}” nie istnieje w zakresie package.json w ścieżce „{1}”.", "Exported_type_alias_0_has_or_is_using_private_name_1_4081": "<PERSON><PERSON> „{0}” wyeksportowanego typu ma nazwę prywatną „{1}” lub używa tej nazwy.", "Exported_type_alias_0_has_or_is_using_private_name_1_from_module_2_4084": "Wyeksportowany alias ty<PERSON> „{0}” ma nazwę prywatną „{1}” z modułu „{2}” lub używa tej nazwy.", "Exported_variable_0_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4023": "Wyeksportowana zmienna „{0}” ma nazwę „{1}” z modułu zewnętrznego {2} lub używa tej nazwy, ale nie można jej nazwa<PERSON>.", "Exported_variable_0_has_or_is_using_name_1_from_private_module_2_4024": "Wyeksportowana zmienna „{0}” ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Exported_variable_0_has_or_is_using_private_name_1_4025": "Wyeksportowana zmienna „{0}” ma nazwę prywatną „{1}” lub używa tej nazwy.", "Exports_and_export_assignments_are_not_permitted_in_module_augmentations_2666": "Eksporty i przypisania eksportów nie są dozwolone w rozszerzeniach modułów.", "Expression_expected_1109": "Oczekiwano wyrażenia.", "Expression_must_be_enclosed_in_parentheses_to_be_used_as_a_decorator_1497": "Wyrażenie musi być ujęte w nawiasy, aby było używane jako dekorator.", "Expression_or_comma_expected_1137": "Oczekiwano wyrażenia lub przecinka.", "Expression_produces_a_tuple_type_that_is_too_large_to_represent_2800": "Wyrażenie tworzy typ krotki, kt<PERSON><PERSON> jest zbyt du<PERSON>, aby go reprez<PERSON><PERSON>.", "Expression_produces_a_union_type_that_is_too_complex_to_represent_2590": "Wyrażenie tworzy typ unii, kt<PERSON><PERSON> jest zbyt złożony, aby go reprezent<PERSON>.", "Expression_resolves_to_super_that_compiler_uses_to_capture_base_class_reference_2402": "Wynikiem rozpoznania wyrażenia jest element „_super” używany przez kompilator do przechwycenia odwołania do klasy bazowej.", "Expression_resolves_to_variable_declaration_newTarget_that_compiler_uses_to_capture_new_target_meta__2544": "Wynikiem rozpoznania wyrażenia jest deklaracja zmiennej „_newTarget” używana przez kompilator do przechwytywania odwołania do metawłaściwości „new.target”.", "Expression_resolves_to_variable_declaration_this_that_compiler_uses_to_capture_this_reference_2400": "Wynikiem rozpoznania wyrażenia jest deklaracja zmiennej „_this” używana przez kompilator do przechwycenia odwołania do elementu „this”.", "Expression_type_can_t_be_inferred_with_isolatedDeclarations_9013": "Nie można wywnioskować typu wyrażenia za pomocą wyrażenia --isolatedDeclarations.", "Extends_clause_can_t_contain_an_expression_with_isolatedDeclarations_9021": "<PERSON><PERSON><PERSON><PERSON> Extends nie może zawierać wyrażenia z parametrem --isolatedDeclarations.", "Extends_clause_for_inferred_type_0_has_or_is_using_private_name_1_4085": "<PERSON><PERSON><PERSON><PERSON> Extends dla wnioskowanego typu „{0}” ma nazwę prywatną „{1}” lub u<PERSON> jej.", "Extract_base_class_to_variable_90064": "Wyodrębnij klasę bazową do zmiennej", "Extract_binding_expressions_to_variable_90066": "Wyodrębnianie wyrażeń powiązania do zmiennej", "Extract_constant_95006": "Wyodręb<PERSON>j s<PERSON>łą", "Extract_default_export_to_variable_90065": "Wyodrębnij domyślny eksport do zmiennej", "Extract_function_95005": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Extract_to_0_in_1_95004": "Wyodrębnij do {0} w {1}", "Extract_to_0_in_1_scope_95008": "Wyodrębnij do {0} w zakresie {1}", "Extract_to_0_in_enclosing_scope_95007": "Wyodrębnij do {0} w zakresie otaczającym", "Extract_to_interface_95090": "Wyodrębnianie do interfejsu", "Extract_to_type_alias_95078": "Wyodrębnianie do aliasu typu", "Extract_to_typedef_95079": "Wyodrębnianie do elementu typedef", "Extract_to_variable_and_replace_with_0_as_typeof_0_90069": "Wyodrębnij do zmiennej i zamień na „{0} as typeof {0}”", "Extract_type_95077": "Typ wyodrębniania", "FILE_6035": "PLIK", "FILE_OR_DIRECTORY_6040": "PLIK LUB KATALOG", "Failed_to_find_peerDependency_0_6283": "Nie można odnaleźć elementu peerDependency „{0}”.", "Failed_to_resolve_under_condition_0_6415": "<PERSON>e można rozpoznać pod warunkiem „{0}”.", "Fallthrough_case_in_switch_7029": "Przepuszczająca klauzula case w instrukcji switch.", "File_0_does_not_exist_6096": "<PERSON><PERSON> „{0}” nie istnieje.", "File_0_does_not_exist_according_to_earlier_cached_lookups_6240": "Biorąc pod uwagę wcześniejsze wyszukiwania w pamięci podręcznej, plik „{0}” nie istnieje.", "File_0_exists_according_to_earlier_cached_lookups_6239": "Biorąc pod uwagę wcześniejsze wyszukiwania w pamięci podręcznej, plik „{0}” istnieje.", "File_0_exists_use_it_as_a_name_resolution_result_6097": "<PERSON><PERSON> „{0}” istnieje — użyj go jako wyniku rozpoznawania nazw.", "File_0_has_an_unsupported_extension_The_only_supported_extensions_are_1_6054": "<PERSON><PERSON> „{0}” ma nieobsługiwane rozszerzenie. Obsługiwane są tylko rozszerzenia {1}.", "File_0_is_a_JavaScript_file_Did_you_mean_to_enable_the_allowJs_option_6504": "P<PERSON> „{0}” jest plikiem JavaScript. <PERSON>zy chodziło Ci o włączenie opcji „allowJs”?", "File_0_is_not_a_module_2306": "<PERSON><PERSON> „{0}” nie jest modułem.", "File_0_is_not_listed_within_the_file_list_of_project_1_Projects_must_list_all_files_or_use_an_includ_6307": "Plik „{0}” nie znajduje się na liście plików projektu „{1}”. Projekty muszą zawierać listę wszystkich plików lub używać wzorca „include”.", "File_0_is_not_under_rootDir_1_rootDir_is_expected_to_contain_all_source_files_6059": "Plik „{0}” nie znajduje się w katalogu „rootDir” „{1}”. Katalog „rootDir” powinien zawierać wszystkie pliki źródłowe.", "File_0_not_found_6053": "<PERSON>e można odnaleźć pliku '{0}'.", "File_Management_6245": "Zarządzanie plikami", "File_appears_to_be_binary_1490": "<PERSON><PERSON> jest prawdopodo<PERSON><PERSON> binarny.", "File_change_detected_Starting_incremental_compilation_6032": "Wykryto zmianę pliku. Trwa rozpoczynanie kompilacji przyrostowej...", "File_is_CommonJS_module_because_0_does_not_have_field_type_1460": "Plik jest modułem CommonJS, ponieważ element „{0}” nie ma pola „type”", "File_is_CommonJS_module_because_0_has_field_type_whose_value_is_not_module_1459": "Plik jest modułem CommonJS, ponieważ element „{0}” ma pole „type”, kt<PERSON>rego warto<PERSON> nie jest „module”", "File_is_CommonJS_module_because_package_json_was_not_found_1461": "Plik jest modułem CommonJS, ponieważ nie znaleziono pliku „package.json”", "File_is_ECMAScript_module_because_0_has_field_type_with_value_module_1458": "Plik jest modułem ECMAScript, ponieważ element „{0}” ma pole „type” z wartością „module”", "File_is_a_CommonJS_module_it_may_be_converted_to_an_ES_module_80001": "Plik jest modułem CommonJS; może zostać przekonwertowany na moduł ES.", "File_is_default_library_for_target_specified_here_1426": "Plik to domyślna biblioteka dla elementu docelowego określonego w tym mi<PERSON>cu.", "File_is_entry_point_of_type_library_specified_here_1419": "Plik to punkt wejścia biblioteki typów określonej w tym mi<PERSON>.", "File_is_included_via_import_here_1399": "Plik jest dołączony w tym miejscu za pomocą importu.", "File_is_included_via_library_reference_here_1406": "Plik jest dołączony w tym miejscu za pomocą odwołania do biblioteki.", "File_is_included_via_reference_here_1401": "Plik jest dołączony w tym miejscu za pomocą odwołania.", "File_is_included_via_type_library_reference_here_1404": "Plik jest dołączony w tym miejscu za pomocą odwołania do biblioteki typów.", "File_is_library_specified_here_1423": "<PERSON>lik to biblioteka określona w tym mi<PERSON>.", "File_is_matched_by_files_list_specified_here_1410": "Plik jest zgodny z listą „files” określoną w tym miejscu.", "File_is_matched_by_include_pattern_specified_here_1408": "Plik jest zgodny z wzorcem dołączania określonym w tym miej<PERSON>.", "File_is_output_from_referenced_project_specified_here_1413": "Plik to dane wyjściowe z przywoływanego projektu określonego w tym miejscu.", "File_is_output_of_project_reference_source_0_1428": "Plik to dane wyjściowe ze źródła odwołania do projektu „{0}”", "File_is_source_from_referenced_project_specified_here_1416": "Plik to źródło z przywoływanego projektu określonego w tym miej<PERSON>cu.", "File_name_0_differs_from_already_included_file_name_1_only_in_casing_1149": "Nazwa pliku „{0}” różni się od już dołączonej nazwy pliku „{1}” tylko wielkością liter.", "File_name_0_has_a_1_extension_looking_up_2_instead_6262": "<PERSON><PERSON><PERSON> p<PERSON> „{0}” ma rozszerzenie „{1}” — zamiast tego wyszukaj „{2}”.", "File_name_0_has_a_1_extension_stripping_it_6132": "Naz<PERSON> p<PERSON> „{0}” ma rozszerzenie „{1}” — zostanie ono usunięte.", "File_redirects_to_file_0_1429": "Plik przekierowuje do pliku „{0}”", "File_specification_cannot_contain_a_parent_directory_that_appears_after_a_recursive_directory_wildca_5065": "Specyfikacja pliku nie może zawierać katalogu nadrzędnego („..”) wyświetlanego po symbolu wieloznacznym katalogu rekursywnego („**”): „{0}”.", "File_specification_cannot_end_in_a_recursive_directory_wildcard_Asterisk_Asterisk_Colon_0_5010": "Specyfikacja pliku nie może kończyć się cyklicznym symbolem wieloznacznym katalogu („**”): „{0}”.", "Filters_results_from_the_include_option_6627": "Filtruj wyniki z opcji „include”.", "Fix_all_detected_spelling_errors_95026": "Napraw wszystkie wykryte błędy pisowni", "Fix_all_expressions_possibly_missing_await_95085": "Napraw wszystkie wyrażenia, w których prawdopodobnie brakuje operatora „await”", "Fix_all_implicit_this_errors_95107": "Usuń wszystkie niejawne błędy „this”", "Fix_all_incorrect_return_type_of_an_async_functions_90037": "Napraw wszystkie niepoprawne zwracane typy funkcji asynchronicznych", "Fix_all_with_type_only_imports_95182": "Napraw wszystko za pomocą importów tylko typów", "Found_0_errors_6217": "Znaleziono błędy: {0}.", "Found_0_errors_Watching_for_file_changes_6194": "Znalezione błędy: {0}. Obserwowanie zmian plików.", "Found_0_errors_in_1_files_6261": "Znaleziono błędy {0} w plikach {1}.", "Found_0_errors_in_the_same_file_starting_at_Colon_1_6260": "Znaleziono błędy {0} w tym samym pliku, zaczynając od: {1}", "Found_1_error_6216": "Znaleziono 1 błąd.", "Found_1_error_Watching_for_file_changes_6193": "Znaleziono 1 błąd. Obserwowanie zmian plików.", "Found_1_error_in_0_6259": "Znaleziono 1 błąd w {0}", "Found_package_json_at_0_6099": "Znaleziono plik „package.json” w lokalizacji „{0}”.", "Found_peerDependency_0_with_1_version_6282": "Znaleziono element peerDependency „{0}” z wersją „{1}”.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_1250": "Deklaracje funkcji nie są dozwolone wewnątrz bloków w trybie z ograniczeniami, jeśli elementem docelowym jest „ES5”.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_Class_definiti_1251": "Deklaracje funkcji nie są dozwolone wewnątrz bloków w trybie z ograniczeniami, jeśli elementem docelowym jest „ES5”. Definicje klas automatycznie używają trybu z ograniczeniami.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_Modules_are_au_1252": "Deklaracje funkcji nie są dozwolone wewnątrz bloków w trybie z ograniczeniami, jeśli elementem docelowym jest „ES5”. Moduły automatycznie używają trybu z ograniczeniami.", "Function_expression_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7011": "Dla wyrażenia funkcji bez adnotacji zwracanego typu jest niejawnie określony zwracany typ „{0}”.", "Function_implementation_is_missing_or_not_immediately_following_the_declaration_2391": "Brak implementacji funkcji lub nie występuje ona bezpośrednio po deklaracji.", "Function_implementation_name_must_be_0_2389": "Implementacja funkcji musi mieć nazwę „{0}”.", "Function_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_ref_7024": "Dla funkcji niejawnie określono zwracany typ „any”, ponieważ nie zawiera ona adnotacji zwracanego typu i jest przywoływana bezpośrednio lub pośrednio w jednym z jej zwracanych wyrażeń.", "Function_lacks_ending_return_statement_and_return_type_does_not_include_undefined_2366": "Funkcja nie zawiera końcowej instrukcji return, a zwracany typ nie obej<PERSON><PERSON> war<PERSON> „undefined”.", "Function_must_have_an_explicit_return_type_annotation_with_isolatedDeclarations_9007": "Funkcja musi mieć jawną adnotację zwracanego typu z wyrażeniem --isolatedDeclarations.", "Function_not_implemented_95159": "Funkcja nie jest zaimplementowana.", "Function_overload_must_be_static_2387": "Przeciążenie funkcji musi być statyczne.", "Function_overload_must_not_be_static_2388": "Przeciążenie funkcji nie może być statyczne.", "Function_type_notation_must_be_parenthesized_when_used_in_a_union_type_1385": "Notacja typu funkcji musi być ujęta w nawiasy, je<PERSON>li jest używana w typie unii.", "Function_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1387": "Notacja typu funkcji musi być ujęta w nawiasy, jeśli jest używana w typie przecięcia.", "Function_type_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7014": "Dla typu funkcji bez adnotacji zwracanego typu jest niejawnie określony zwracany typ „{0}”.", "Function_with_bodies_can_only_merge_with_classes_that_are_ambient_2814": "Funkcja z treścią może tylko scalać z klasami, które są otaczające.", "Generate_d_ts_files_from_TypeScript_and_JavaScript_files_in_your_project_6612": "Generuj pliki d.ts z plików TypeScript i JavaScript w projekcie.", "Generate_get_and_set_accessors_95046": "<PERSON><PERSON><PERSON> met<PERSON> „get” i „set”.", "Generate_get_and_set_accessors_for_all_overriding_properties_95119": "Gene<PERSON><PERSON> metody <PERSON> „get” i „set” dla wszystkich właściwości przesłaniających", "Generates_a_CPU_profile_6223": "Generuje profil procesora CPU.", "Generates_a_sourcemap_for_each_corresponding_d_ts_file_6000": "Generuje mapę źródła dla poszczególnych plików „.d.ts”.", "Generates_an_event_trace_and_a_list_of_types_6237": "Generuje śledzenie zdarzeń i listę typów.", "Generates_corresponding_d_ts_file_6002": "Generuje odpowiadający plik „d.ts”.", "Generates_corresponding_map_file_6043": "Generuje odpowiadający plik „map”.", "Generator_implicitly_has_yield_type_0_because_it_does_not_yield_any_values_Consider_supplying_a_retu_7025": "Dla generatora niejawnie określono zwracany typ „{0}”, ponieważ nie zwraca on żadnych wartości. Rozważ podanie adnotacji zwracanego typu.", "Generators_are_not_allowed_in_an_ambient_context_1221": "Generatory nie są dozwolone w otaczającym kontekście.", "Generic_type_0_requires_1_type_argument_s_2314": "<PERSON><PERSON>y „{0}” wymaga następującej liczby argumentów typu: {1}.", "Generic_type_0_requires_between_1_and_2_type_arguments_2707": "<PERSON><PERSON>y „{0}” wymaga od {1} do {2} argumentów typu.", "Global_module_exports_may_only_appear_at_top_level_1316": "Globalne eksporty modułu mogą występować tylko na najwyższym poziomie.", "Global_module_exports_may_only_appear_in_declaration_files_1315": "Globalne eksporty modułu mogą występować tylko w plikach deklaracji.", "Global_module_exports_may_only_appear_in_module_files_1314": "Globalne eksporty modułu mogą występować tylko w plikach modułów.", "Global_type_0_must_be_a_class_or_interface_type_2316": "Typ globalny „{0}” musi być typem klasy lub interfejsu.", "Global_type_0_must_have_1_type_parameter_s_2317": "Typ globalny „{0}” musi mieć następującą liczbę parametrów typu: {1}.", "Have_recompiles_in_incremental_and_watch_assume_that_changes_within_a_file_will_only_affect_files_di_6384": "W przypadku ponownego kompilowania w parametrach „--incremental” i „--watch” przyj<PERSON><PERSON> się, że zmiany w pliku będą miały wpływ tylko na pliki bezpośrednio od niego zależne.", "Have_recompiles_in_projects_that_use_incremental_and_watch_mode_assume_that_changes_within_a_file_wi_6606": "<PERSON><PERSON><PERSON><PERSON>, że zmiany w pliku będą wpływać tylko na pliki bezpośrednio od niego zależne podczas ponownego kompilowania w projektach używających trybów „przyrostowo” i „obserwacja”.", "Hexadecimal_digit_expected_1125": "Oczekiwano cyfry szesnastkowej.", "Identifier_expected_0_is_a_reserved_word_at_the_top_level_of_a_module_1262": "Oczekiwano identyfikatora. „{0}” jest słowem zastrzeżonym na najwyższym poziomie modułu.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_1212": "Oczekiwano identyfikatora. „{0}” jest wyrazem zastrzeżonym w trybie z ograniczeniami.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Class_definitions_are_automatically_in_stric_1213": "Oczekiwano identyfikatora. „{0}” jest wyrazem zastrzeżonym w trybie z ograniczeniami. Definicje klas są określane automatycznie w trybie z ograniczeniami.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Modules_are_automatically_in_strict_mode_1214": "Oczekiwano identyfikatora. Element „{0}” jest wyrazem zastrzeżonym w trybie z ograniczeniami. Moduły są określane automatycznie w trybie z ograniczeniami.", "Identifier_expected_0_is_a_reserved_word_that_cannot_be_used_here_1359": "Oczekiwano identyfikatora. „{0}” jest słowem zastrzeżonym, którego nie można użyć w tym miej<PERSON>.", "Identifier_expected_1003": "Oczekiwano identyfikatora.", "Identifier_expected_esModule_is_reserved_as_an_exported_marker_when_transforming_ECMAScript_modules_1216": "Oczekiwano identyfikatora. Ciąg „__esModule” jest zastrzeżony jako eksportowany znacznik podczas transformowania modułów ECMAScript.", "Identifier_or_string_literal_expected_1478": "Oczekiwano identyfikatora lub literału ciągu.", "Identifier_string_literal_or_number_literal_expected_1496": "Oczekiwano identyfikatora, literału ciągu lub literału liczbowego.", "If_the_0_package_actually_exposes_this_module_consider_sending_a_pull_request_to_amend_https_Colon_S_7040": "<PERSON><PERSON><PERSON> pakiet \"{0}\" faktycznie udostępnia ten moduł, rozważ wysłanie żądania ściągnięcia w celu zmiany elementu \"https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/{1}\"", "If_the_0_package_actually_exposes_this_module_try_adding_a_new_declaration_d_ts_file_containing_decl_7058": "<PERSON><PERSON><PERSON> p<PERSON> „{0}” rzeczywiście uwidacznia ten moduł, spró<PERSON>j dodać nowy plik deklaracji (.d.ts) zawierający „declare module”{1}';`", "Ignore_this_error_message_90019": "Ignoruj ten komunikat o błędzie", "Ignoring_tsconfig_json_compiles_the_specified_files_with_default_compiler_options_6924": "Ignorując plik tsconfig.json, kompiluje określone pliki z domyślnymi opcjami kompilatora.", "Implement_all_inherited_abstract_classes_95040": "Zaimplementuj wszystkie dziedziczone klasy abstrakcyjne", "Implement_all_unimplemented_interfaces_95032": "Zaimplementuj wszystkie niezaimplementowane interfejsy", "Implement_inherited_abstract_class_90007": "<PERSON><PERSON><PERSON>ż odziedziczoną klasę abstrakcyjną", "Implement_interface_0_90006": "Implement<PERSON>j interfejs „{0}”", "Implements_clause_of_exported_class_0_has_or_is_using_private_name_1_4019": "<PERSON><PERSON><PERSON><PERSON> implements wyeksportowanej klasy „{0}” ma nazwę prywatną „{1}” lub używa tej nazwy.", "Implicit_conversion_of_a_symbol_to_a_string_will_fail_at_runtime_Consider_wrapping_this_expression_i_2731": "Niejawna konwersja typu „symbol” na „string” zakończy się niepowodzeniem w czasie wykonywania. Rozważ opakowywanie tego wyrażenia w elemencie „String(...)”.", "Import_0_conflicts_with_global_value_used_in_this_file_so_must_be_declared_with_a_type_only_import_w_2866": "Import „{0}\" powoduje konflikt z wartością globalną używaną w tym pliku, dlatego musi być zadeklarowany z importem tylko typu, gdy jest włączona opcja „isolatedModules”.", "Import_0_conflicts_with_local_value_so_must_be_declared_with_a_type_only_import_when_isolatedModules_2865": "Import „{0}” powoduje konflikt z wartością lokalną, dlatego musi być zadeklarowany za pomocą importu tylko typu, gdy jest włączona opcja „isolatedModules”.", "Import_0_from_1_90013": "Importuj element „{0}” z lokalizacji „{1}”", "Import_assertion_values_must_be_string_literal_expressions_2837": "Wartości atrybutu importu muszą być wyrażeniami literału ciągu.", "Import_assertions_are_not_allowed_on_statements_that_compile_to_CommonJS_require_calls_2836": "Asercje importu są niedozwolone w instrukcjach, które kompilują do wywołań „require” CommonJS.", "Import_assertions_are_only_supported_when_the_module_option_is_set_to_esnext_nodenext_or_preserve_2821": "Asercje importu są obsługiwane tylko wtedy, gdy opcja „--module” jest ustawiona na wartość „esnext”, „nodenext” lub „preserve”.", "Import_assertions_cannot_be_used_with_type_only_imports_or_exports_2822": "Asercji importu nie można używać z importami ani eksportami ograniczonymi do tylko danego typu.", "Import_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_import_Asterisk_as_1202": "Nie można użyć przypisania importu, gdy są używane moduły języka ECMAScript. Zamiast tego rozważ użycie elementu „import * as ns from \"mod\"”, „import {a} from \"mod\"” lub „import d from \"mod\"” albo innego formatu modułu.", "Import_attribute_values_must_be_string_literal_expressions_2858": "Wartości atrybutów importu muszą być wyrażeniami literału ciągu.", "Import_attributes_are_not_allowed_on_statements_that_compile_to_CommonJS_require_calls_2856": "Atrybuty importu są niedozwolone w instrukcjach kompilowanych do wywołań „require” CommonJS.", "Import_attributes_are_only_supported_when_the_module_option_is_set_to_esnext_nodenext_or_preserve_2823": "Atrybuty importu są obsługiwane tylko wtedy, gdy opcja „--module” jest ustawiona na wartość „esnext”, „nodenext” lub „preserve”.", "Import_attributes_cannot_be_used_with_type_only_imports_or_exports_2857": "Atrybutów importu nie można używać z importami ani eksportami tylko typów.", "Import_declaration_0_is_using_private_name_1_4000": "Deklarac<PERSON> importu „{0}” używa nazwy prywatnej „{1}”.", "Import_declaration_conflicts_with_local_declaration_of_0_2440": "Deklaracja importu powoduje konflikt z deklaracją lokalną „{0}”.", "Import_declarations_in_a_namespace_cannot_reference_a_module_1147": "Deklaracje importu w przestrzeni nazw nie mogą odwoływać się do modułu.", "Import_emit_helpers_from_tslib_6139": "Importuj pomocników emitowania z elementu „tslib”.", "Import_may_be_converted_to_a_default_import_80003": "Import może zostać przekonwertowany na import domyślny.", "Import_name_cannot_be_0_2438": "Import nie może mieć nazwy „{0}”.", "Import_or_export_declaration_in_an_ambient_module_declaration_cannot_reference_module_through_relati_2439": "Deklaracja importu lub eksportu w deklaracji otaczającego modułu nie może przywoływać modułu za pomocą jego nazwy względnej.", "Import_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6271": "Specyfikator importu „{0}” nie istnieje w zakresie package.json w ścieżce „{1}”.", "Imported_via_0_from_file_1_1393": "Zaimportowano za pośrednictwem elementu {0} z pliku „{1}”", "Imported_via_0_from_file_1_to_import_importHelpers_as_specified_in_compilerOptions_1395": "Zaimportowano za pośrednictwem elementu {0} z pliku „{1}” w celu zaimportowania elementów „importHelpers” zgodnie z opcjami compilerOptions", "Imported_via_0_from_file_1_to_import_jsx_and_jsxs_factory_functions_1397": "Zaimportowano za pośrednictwem elementu {0} z pliku „{1}” w celu zaimportowania funkcji fabryki „jsx” i „jsxs”", "Imported_via_0_from_file_1_with_packageId_2_1394": "Zaimportowano za pośrednictwem elementu {0} z pliku „{1}” o identyfikatorze packageId „{2}”", "Imported_via_0_from_file_1_with_packageId_2_to_import_importHelpers_as_specified_in_compilerOptions_1396": "Zaimportowano za pośrednictwem elementu {0} z pliku „{1}” o identyfikatorze packageId „{2}” w celu zaimportowania elementów „importHelpers” zgodnie z opcjami compilerOptions", "Imported_via_0_from_file_1_with_packageId_2_to_import_jsx_and_jsxs_factory_functions_1398": "Zaimportowano za pośrednictwem elementu {0} z pliku „{1}” o identyfikatorze packageId „{2}” w celu zaimportowania funkcji fabryki „jsx” i „jsxs”", "Imports_are_not_permitted_in_module_augmentations_Consider_moving_them_to_the_enclosing_external_mod_2667": "Importy nie są dozwolone w rozszerzeniach modułów. Rozważ przeniesienie ich do obejmującego modułu zewnętrznego.", "In_ambient_enum_declarations_member_initializer_must_be_constant_expression_1066": "W deklaracjach wyliczenia otoczenia inicjator składowej musi być wyrażeniem stałym.", "In_an_enum_with_multiple_declarations_only_one_declaration_can_omit_an_initializer_for_its_first_enu_2432": "W przypadku wyliczenia z wieloma deklaracjami tylko jedna deklaracja może pominąć inicjator dla pierwszego elementu wyliczenia.", "Include_a_list_of_files_This_does_not_support_glob_patterns_as_opposed_to_include_6635": "Dołącz listę plików. Nie obsługuje to wzorców globalnych, w przeciwieństwie do elementu „include”.", "Include_modules_imported_with_json_extension_6197": "Uwzględnij moduły zaimportowane z rozszerzeniem „json”", "Include_source_code_in_the_sourcemaps_inside_the_emitted_JavaScript_6644": "Uwzględnij kod źródłowy w mapach źródła wewnątrz emitowanego kodu JavaScript.", "Include_sourcemap_files_inside_the_emitted_JavaScript_6643": "Uwzględnij pliki mapy źródła w emitowanym kodzie JavaScript.", "Includes_imports_of_types_referenced_by_0_90054": "Obejmuje importy typów przywoływanych przez element \"{0}\"", "Including_watch_w_will_start_watching_the_current_project_for_the_file_changes_Once_set_you_can_conf_6914": "Zawarcie elementu --watch spowo<PERSON><PERSON>, że -w rozpocznie obserwowanie bieżącego projektu w kierunku zmian w plikach. Po skonfigurowaniu ustawienia możesz określić tryb obserwacji za pomocą:", "Incomplete_quantifier_Digit_expected_1505": "Niekompletny kwantyfikator. Oczekiwano cyfry.", "Index_signature_for_type_0_is_missing_in_type_1_2329": "<PERSON><PERSON> sygnatury in<PERSON><PERSON> dla typu „{0}” w typie „{1}”.", "Index_signature_in_type_0_only_permits_reading_2542": "Sygnatura indeksu w typie „{0}” zezwala tylko na odczytywanie.", "Individual_declarations_in_merged_declaration_0_must_be_all_exported_or_all_local_2395": "Wszystkie poszczególne deklaracje w scalonej deklaracji „{0}” muszą być wyeksportowane lub lokalne.", "Infer_all_types_from_usage_95023": "Wywnioskuj wszystkie typy na podstawie użycia", "Infer_function_return_type_95148": "Wnioskuj zwracany typ funkcji", "Infer_parameter_types_from_usage_95012": "Wnioskuj typy parametrów na podstawie użycia", "Infer_this_type_of_0_from_usage_95080": "Wnioskuj typ „this” elementu „{0}” na podstawie użycia", "Infer_type_of_0_from_usage_95011": "Wnioskuj typ elementu „{0}” na podstawie użycia", "Inference_from_class_expressions_is_not_supported_with_isolatedDeclarations_9022": "Wnioskowanie z wyrażeń klasy nie jest obsługiwane w przypadku wyrażenia --isolatedDeclarations.", "Initialize_property_0_in_the_constructor_90020": "Zainicju<PERSON><PERSON><PERSON><PERSON> „{0}” w konstruktorze", "Initialize_static_property_0_90021": "Zainicjuj właściwość statyczną „{0}”", "Initializer_for_property_0_2811": "<PERSON><PERSON><PERSON><PERSON> \"{0}\"", "Initializer_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2301": "Inicjator zmiennej składowej wystąpienia „{0}” nie może przywoływać identyfikatora „{1}” zadeklarowanego w konstruktorze.", "Initializers_are_not_allowed_in_ambient_contexts_1039": "Inicjatory są niedozwolone w otaczających kontekstach.", "Initializes_a_TypeScript_project_and_creates_a_tsconfig_json_file_6070": "Inicjuje projekt TypeScript i tworzy plik tsconfig.json.", "Inline_variable_95184": "Zmienna wbudowana", "Insert_command_line_options_and_files_from_a_file_6030": "Wstaw opcje wiersza polecenia i pliki z pliku.", "Install_0_95014": "Zainstaluj składnik „{0}”", "Install_all_missing_types_packages_95033": "Zainstaluj wszystkie brakujące pakiety typów", "Interface_0_cannot_simultaneously_extend_types_1_and_2_2320": "Interfejs „{0}” nie może jednocześnie rozszerzać typów „{1}” i „{2}”.", "Interface_0_incorrectly_extends_interface_1_2430": "Interfejs „{0}” niepoprawnie rozszerza interfejs „{1}”.", "Interface_declaration_cannot_have_implements_clause_1176": "Deklaracja interfejsu nie może mieć klauzuli „implements”.", "Interface_must_be_given_a_name_1438": "Interfejs musi mieć nazwę.", "Interface_name_cannot_be_0_2427": "Interfejs nie może mieć nazwy „{0}”.", "Interop_Constraints_6252": "Ograniczenia międzyoperacyjności", "Interpret_optional_property_types_as_written_rather_than_adding_undefined_6243": "Interpretuj opcjonalne typy właściwości jako zapisane, zamiast dodawać element \"undefined\".", "Invalid_character_1127": "Nieprawidłowy znak.", "Invalid_import_specifier_0_has_no_possible_resolutions_6272": "Nieprawidłowy specyfikator importu „{0}” nie ma możliwych rozwiązań.", "Invalid_module_name_in_augmentation_Module_0_resolves_to_an_untyped_module_at_1_which_cannot_be_augm_2665": "Nieprawidłowa nazwa modułu w rozszerzeniu. <PERSON><PERSON><PERSON> „{0}” jest rozpoznawany jako moduł bez typu na poziomie „{1}”, którego nie można rozszerzyć.", "Invalid_module_name_in_augmentation_module_0_cannot_be_found_2664": "Nieprawidłowa nazwa modułu w rozszerzeniu. Nie można znaleźć modułu „{0}”.", "Invalid_optional_chain_from_new_expression_Did_you_mean_to_call_0_1209": "Nieprawidłowy opcjonalny łańcuch z nowego wyrażenia. <PERSON>zy chodziło Ci o wywołanie „{0}()”?", "Invalid_reference_directive_syntax_1084": "Nieprawidłowa składnia dyrektywy „reference”.", "Invalid_syntax_in_decorator_1498": "Nieprawidłowa składnia w dekoratorze.", "Invalid_use_of_0_It_cannot_be_used_inside_a_class_static_block_18039": "Nieprawidłowe użycie elementu \"{0}\". Nie można go użyć wewnątrz bloku statycznego klasy.", "Invalid_use_of_0_Modules_are_automatically_in_strict_mode_1215": "Nieprawidłowe użycie elementu „{0}”. Moduły są określane automatycznie w trybie z ograniczeniami.", "Invalid_use_of_0_in_strict_mode_1100": "Nieprawidłowe użycie elementu „{0}” w trybie z ograniczeniami.", "Invalid_value_for_ignoreDeprecations_5103": "Nieprawidłowa wartość parametru „--ignoreDeprecations”.", "Invalid_value_for_jsxFactory_0_is_not_a_valid_identifier_or_qualified_name_5067": "Nieprawidłowa wartość elementu „jsxFactory”. „{0}” to nie jest prawidłowy identyfikator ani kwalifikowana nazwa.", "Invalid_value_for_jsxFragmentFactory_0_is_not_a_valid_identifier_or_qualified_name_18035": "Nieprawidłowa wartość elementu „jsxFragmentFactory”. „{0}” nie jest prawidłowym identyfikatorem ani kwalifikowaną nazwą.", "Invalid_value_for_reactNamespace_0_is_not_a_valid_identifier_5059": "Nieprawidłowa wartość opcji „--reactNamespace”. Element „{0}” nie jest prawidłowym identyfikatorem.", "It_is_likely_that_you_are_missing_a_comma_to_separate_these_two_template_expressions_They_form_a_tag_2796": "Prawdopodobnie brakuje prz<PERSON>a, aby <PERSON><PERSON><PERSON> te dwa wyrażenia szablonu. Tworzą one wyrażenie szablonu z tagami, którego nie można wywołać.", "Its_element_type_0_is_not_a_valid_JSX_element_2789": "<PERSON><PERSON> typ elementu „{0}” nie jest prawidłowym elementem JSX.", "Its_instance_type_0_is_not_a_valid_JSX_element_2788": "<PERSON><PERSON> typ wystąpienia „{0}” nie jest prawidłowym elementem JSX.", "Its_return_type_0_is_not_a_valid_JSX_element_2787": "<PERSON><PERSON> zwracany typ „{0}” nie jest prawidłowym elementem JSX.", "Its_type_0_is_not_a_valid_JSX_element_type_18053": "<PERSON><PERSON> typ „{0}” nie jest prawidłowym typem rozszerzenia JSX.", "JSDoc_0_1_does_not_match_the_extends_2_clause_8023": "Element JSDoc „@{0} {1}” nie pasuje do klauzuli „extends {2}”.", "JSDoc_0_is_not_attached_to_a_class_8022": "Element JSDoc „@{0}” nie został dołączony do klasy.", "JSDoc_may_only_appear_in_the_last_parameter_of_a_signature_8028": "Element „...” JSDoc może występować tylko w ostatnim parametrze sygnatury.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_8024": "Tag JSDoc „@param” tag ma nazwę „{0}”, ale nie ma parametru o tej nazwie.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_It_would_match_arguments_if_it_h_8029": "Tag JSDoc „@param” ma nazwę „{0}”, ale nie istnieje parametr o tej nazwie. Byłby on zgodny z elementem „arguments”, gdyby miał typ tablicy.", "JSDoc_typedef_may_be_converted_to_TypeScript_type_80009": "Element JSDoc typedef można przekonwertować na typ TypeScript.", "JSDoc_typedef_tag_should_either_have_a_type_annotation_or_be_followed_by_property_or_member_tags_8021": "Tag „@typedef” JSDoc powinien mieć adnotację typu lub powinien po nim następować tag „@property” lub „@member”.", "JSDoc_typedefs_may_be_converted_to_TypeScript_types_80010": "Element JSDoc typedefs można przekonwertować na typy TypeScript.", "JSDoc_types_can_only_be_used_inside_documentation_comments_8020": "Typy JSDoc mogą być używane wyłącznie w komentarzach dokumentacji.", "JSDoc_types_may_be_moved_to_TypeScript_types_80004": "Typy JSDoc mogą być przenoszone do typów TypeScript.", "JSX_attributes_must_only_be_assigned_a_non_empty_expression_17000": "Atrybuty JSX muszą mieć przypisane wyrażenie, które nie jest puste.", "JSX_element_0_has_no_corresponding_closing_tag_17008": "Element JSX „{0}” nie ma odpowiedniego tagu zamykającego.", "JSX_element_class_does_not_support_attributes_because_it_does_not_have_a_0_property_2607": "Klasa elementów JSX nie obsługuje atrybutów, ponieważ nie ma właściwości „{0}”.", "JSX_element_implicitly_has_type_any_because_no_interface_JSX_0_exists_7026": "Dla elementu JSX niejawnie określono typ „any”, ponieważ interfejs „JSX.{0}” nie istnieje.", "JSX_element_implicitly_has_type_any_because_the_global_type_JSX_Element_does_not_exist_2602": "Dla elementu JSX niejawnie określono typ „any”, ponieważ typ globalny „JSX.Element” nie istnieje.", "JSX_element_type_0_does_not_have_any_construct_or_call_signatures_2604": "Typ elementu JSX „{0}” nie ma sygnatury konstrukcji ani wywołania.", "JSX_elements_cannot_have_multiple_attributes_with_the_same_name_17001": "Elementy JSX nie mogą mieć wielu atrybutów o tej samej nazwie.", "JSX_expressions_may_not_use_the_comma_operator_Did_you_mean_to_write_an_array_18007": "Wyrażenia JSX nie mogą używać operatora przecinka. <PERSON>zy chodziło Ci o zapisanie tablicy?", "JSX_expressions_must_have_one_parent_element_2657": "Wyrażenia JSX muszą mieć jeden element nadrzędny.", "JSX_fragment_has_no_corresponding_closing_tag_17014": "Fragment kodu JSX nie ma odpowiedniego tagu zamykającego.", "JSX_property_access_expressions_cannot_include_JSX_namespace_names_2633": "Wyrażenia dostępu do właściwości JSX nie mogą zawierać nazw przestrzeni nazw JSX", "JSX_spread_child_must_be_an_array_type_2609": "Element podrzędny rozkładu JSX musi być typem tablicy.", "JavaScript_Support_6247": "Pomoc techniczna języka JavaScript", "Jump_target_cannot_cross_function_boundary_1107": "Cel skoku nie może przekraczać granicy funk<PERSON>ji.", "KIND_6034": "RODZAJ", "Keywords_cannot_contain_escape_characters_1260": "Słowa kluczowe nie mogą zawierać znaków ucieczki.", "LOCATION_6037": "LOKALIZACJA", "Language_and_Environment_6254": "Język i środowisko", "Left_side_of_comma_operator_is_unused_and_has_no_side_effects_2695": "Lewa strona operatora „przecinek” jest nieużywana i nie ma żadnych efektów ubocznych.", "Library_0_specified_in_compilerOptions_1422": "Biblioteka „{0}” została określona w opcjach compilerOptions", "Library_referenced_via_0_from_file_1_1405": "Biblioteka jest przywoływana za pośrednictwem elementu „{0}” z pliku „{1}”", "Line_break_not_permitted_here_1142": "Podział wiersza nie jest tutaj dozwolony.", "Line_terminator_not_permitted_before_arrow_1200": "Terminator wiersza nie jest dozwolony przed strzałką.", "List_of_file_name_suffixes_to_search_when_resolving_a_module_6931": "Lista sufiksów nazw plików do przeszukania podczas rozpoznawania modułu.", "List_of_folders_to_include_type_definitions_from_6161": "Lista folderów, z których mają być uwzględnione definicje typów.", "List_of_root_folders_whose_combined_content_represents_the_structure_of_the_project_at_runtime_6168": "Lista folderów głównych, których połączona zawartość reprezentuje strukturę projektu w czasie wykonywania.", "Loading_0_from_the_root_dir_1_candidate_location_2_6109": "Ładowanie elementu „{0}” z katalogu głównego „{1}”, lokalizacja kandydata: „{2}”.", "Loading_module_0_from_node_modules_folder_target_file_types_Colon_1_6098": "Ładowanie modułu „{0}” z folderu „node_modules”, docelowy typ pliku: „{1}”.", "Loading_module_as_file_Slash_folder_candidate_module_location_0_target_file_types_Colon_1_6095": "Ładowanie modułu jako pliku/folderu, lokalizacja modułu kandydata: „{0}”, docelowy typ pliku: „{1}”.", "Locale_must_be_of_the_form_language_or_language_territory_For_example_0_or_1_6048": "Ustawienia regionalne muszą mieć postać <język> lub <język>-<terytorium>. <PERSON> przykład „{0}” lub „{1}”.", "Log_paths_used_during_the_moduleResolution_process_6706": "Rejestruj ścieżki używane podczas procesu „moduleResolution”.", "Longest_matching_prefix_for_0_is_1_6108": "Najdłuższy zgodny prefiks dla „{0}” to „{1}”.", "Looking_up_in_node_modules_folder_initial_location_0_6125": "Wyszukiwanie w folderze „node_modules”, początkowa lokalizacja: „{0}”.", "Make_all_super_calls_the_first_statement_in_their_constructor_95036": "Wszystkie wywołania „super()” powinny być pier<PERSON> instrukcją w konstruktorze", "Make_keyof_only_return_strings_instead_of_string_numbers_or_symbols_Legacy_option_6650": "<PERSON><PERSON><PERSON><PERSON>, że element keyof ma zwracać tylko ciągi zamiast ciągów, liczb i symboli. Starsza opcja.", "Make_super_call_the_first_statement_in_the_constructor_90002": "Ustaw wywołanie „super()” jako pierwsz<PERSON> instrukcję w konstruktorze", "Mapped_object_type_implicitly_has_an_any_template_type_7039": "Zmapowany typ obiektu niejawnie ma typ szablonu „any”.", "Mark_array_literal_as_const_90070": "Oznaczanie literału tablicy jako stałego", "Matched_0_condition_1_6403": "<PERSON><PERSON><PERSON><PERSON> „{0}” warunku „{1}”.", "Matched_by_default_include_pattern_Asterisk_Asterisk_Slash_Asterisk_1457": "Dopasowywane domyślnie do wzorca dołączania „**/*”", "Matched_by_include_pattern_0_in_1_1407": "Zgodne z wzorcem dołączania „{0}” w elemencie „{1}”", "Member_0_implicitly_has_an_1_type_7008": "<PERSON><PERSON> s<PERSON>dowej „{0}” niejawnie określono typ „{1}”.", "Member_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7045": "<PERSON><PERSON> <PERSON><PERSON><PERSON> „{0}” niejawnie ma typ „{1}”, ale lepszy typ można wywnioskować na podstawie użycia.", "Merge_conflict_marker_encountered_1185": "Napotkano znacznik konfliktu scalania.", "Merged_declaration_0_cannot_include_a_default_export_declaration_Consider_adding_a_separate_export_d_2652": "Scalona deklaracja „{0}” nie może zawierać domyślnej deklaracji eksportu. Rozważ dodanie oddzielnej deklaracji „export default {0}” zamiast niej.", "Meta_property_0_is_only_allowed_in_the_body_of_a_function_declaration_function_expression_or_constru_17013": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” jest dozwolona tylko w treści deklaracji funkcji, wyrażeniu funkcji lub konstruktorze.", "Method_0_cannot_have_an_implementation_because_it_is_marked_abstract_1245": "<PERSON><PERSON> „{0}” nie może mi<PERSON>, poniew<PERSON>ż jest oznaczona jako abstrakcyjna.", "Method_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4101": "Metoda „{0}” wyeksportowanego interfejsu ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Method_0_of_exported_interface_has_or_is_using_private_name_1_4102": "Metoda „{0}” wyeksportowanego interfejsu ma nazwę prywatną „{1}” lub używa tej nazwy.", "Method_must_have_an_explicit_return_type_annotation_with_isolatedDeclarations_9008": "Metoda musi mieć jawną adnotację zwracanego typu z wyrażeniem --isolatedDeclarations.", "Method_not_implemented_95158": "Metoda nie jest zaimplementowana.", "Modifiers_cannot_appear_here_1184": "Modyfikatory nie mogą występować w tym mi<PERSON>.", "Module_0_can_only_be_default_imported_using_the_1_flag_1259": "<PERSON><PERSON><PERSON> „{0}” może być importowany domyślnie tylko przy użyciu flagi „{1}”", "Module_0_cannot_be_imported_using_this_construct_The_specifier_only_resolves_to_an_ES_module_which_c_1471": "Nie można zaimportować modułu „{0}” przy użyciu tej konstrukcji. Specyfikator jest rozpoznawany tylko jako moduł ES, którego nie można zaimportować za pomocą wywołania „require”. Zamiast tego użyj importu ECMAScript.", "Module_0_declares_1_locally_but_it_is_exported_as_2_2460": "<PERSON><PERSON><PERSON> „{0}” deklaruje element „{1}” lokalnie, ale jest on eksportowany jako „{2}”.", "Module_0_declares_1_locally_but_it_is_not_exported_2459": "<PERSON><PERSON><PERSON> „{0}” deklaruje element „{1}” lokalnie, ale nie jest on eksportowany.", "Module_0_does_not_refer_to_a_type_but_is_used_as_a_type_here_Did_you_mean_typeof_import_0_1340": "<PERSON><PERSON><PERSON> „{0}” nie odwołuje się do typu, ale jest tutaj używany jako typ. <PERSON><PERSON> chodziło Ci o „typeof import(„{0}”)”?", "Module_0_does_not_refer_to_a_value_but_is_used_as_a_value_here_1339": "<PERSON><PERSON><PERSON> „{0}” nie odwołuje się do wartości, ale jest tutaj używany jako warto<PERSON>.", "Module_0_has_already_exported_a_member_named_1_Consider_explicitly_re_exporting_to_resolve_the_ambig_2308": "Moduł {0} już wyeksportował składową o nazwie „{1}”. Zastanów się nad jawnym ponownym eksportem, aby roz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> niej<PERSON>.", "Module_0_has_no_default_export_1192": "<PERSON><PERSON><PERSON> „{0}” nie ma eksportu domyślnego.", "Module_0_has_no_default_export_Did_you_mean_to_use_import_1_from_0_instead_2613": "<PERSON><PERSON><PERSON> „{0}” nie ma eksportu domyślnego. <PERSON><PERSON> zamiast tego miał zostać użyty element „import { {1} } from {0}”?", "Module_0_has_no_exported_member_1_2305": "<PERSON><PERSON><PERSON> „{0}” nie ma wyeksportowanej składowej „{1}”.", "Module_0_has_no_exported_member_1_Did_you_mean_to_use_import_1_from_0_instead_2614": "<PERSON><PERSON><PERSON> „{0}” nie ma wyeksportowanego elementu członkowskiego „{1}”. <PERSON><PERSON> z<PERSON>st tego miał zostać użyty element „import {1} from {0}”?", "Module_0_is_hidden_by_a_local_declaration_with_the_same_name_2437": "<PERSON><PERSON><PERSON> „{0}” został ukryty przez deklarację lokalną o takiej samej nazwie.", "Module_0_uses_export_and_cannot_be_used_with_export_Asterisk_2498": "<PERSON><PERSON><PERSON> „{0}” używa elementu „export =” i nie może być używany z elementem „export *”.", "Module_0_was_resolved_as_locally_declared_ambient_module_in_file_1_6144": "<PERSON><PERSON><PERSON> „{0}” został rozpoznany jako otaczający moduł zadeklarowany lokalnie w pliku „{1}”.", "Module_0_was_resolved_to_1_but_allowArbitraryExtensions_is_not_set_6263": "<PERSON><PERSON><PERSON> „{0}” został rozpoznany jako „{1}”, ale nie ustawiono parametru „--allowArbitraryExtensions”.", "Module_0_was_resolved_to_1_but_jsx_is_not_set_6142": "<PERSON><PERSON><PERSON> „{0}” został rozpoznany jako „{1}”, ale nie jest ustawiona opcja „--jsx”.", "Module_0_was_resolved_to_1_but_resolveJsonModule_is_not_used_7042": "<PERSON><PERSON><PERSON> „{0}” został rozpoznany jako „{1}”, ale opcja „--resolveJsonModule” nie jest używana.", "Module_declaration_names_may_only_use_or_quoted_strings_1443": "W nazwach deklaracji modułu można używać tylko ciągi ujęte w cudzysłów \" lub „”.", "Module_name_0_matched_pattern_1_6092": "Nazwa modułu: „{0}”, dopasowany wzorzec: „{1}”.", "Module_name_0_was_not_resolved_6090": "======== Nazwa modułu „{0}” nie została rozpoznana. ========", "Module_name_0_was_successfully_resolved_to_1_6089": "======== Nazwa modułu „{0}” została pomyślnie rozpoznana jako „{1}”. ========", "Module_name_0_was_successfully_resolved_to_1_with_Package_ID_2_6218": "======== Nazwa modułu „{0}” została pomyślnie rozpoznana jako „{1}” z identyfikatorem pakietu „{2}”. ========", "Module_resolution_kind_is_not_specified_using_0_6088": "Rodzaj rozpoznawania modułów nie został podany. Zostanie użyty rodzaj „{0}”.", "Module_resolution_using_rootDirs_has_failed_6111": "Nie można rozpoznać modułów przy użyciu opcji „rootDirs”.", "Modules_6244": "<PERSON><PERSON><PERSON><PERSON>", "Move_labeled_tuple_element_modifiers_to_labels_95117": "Przenieś modyfikatory elementów krotki z etykietami do etykiet", "Move_the_expression_in_default_export_to_a_variable_and_add_a_type_annotation_to_it_9036": "Przenieś wyrażenie w eksporcie domyślnym do zmiennej i dodaj do niej adnotację typu.", "Move_to_a_new_file_95049": "Przenieś do nowego pliku", "Move_to_file_95178": "Przenieś do pliku", "Multiple_consecutive_numeric_separators_are_not_permitted_6189": "Kolejne następujące po sobie separatory liczbowe nie są dozwolone.", "Multiple_constructor_implementations_are_not_allowed_2392": "Konstruktor nie może mieć wielu implementacji.", "NEWLINE_6061": "NOWY WIERSZ", "Name_is_not_valid_95136": "Nazwa nie jest prawidłowa", "Named_capturing_groups_are_only_available_when_targeting_ES2018_or_later_1503": "Nazwane grupy przechwytywania są dostępne tylko w przypadku wartości docelowej „ES2018” lub nowszej.", "Named_capturing_groups_with_the_same_name_must_be_mutually_exclusive_to_each_other_1515": "Nazwane grupy przechwytywania o tej samej nazwie muszą się wzajemnie wykluczać.", "Named_property_0_of_types_1_and_2_are_not_identical_2319": "Nazwane właściwości „{0}” typów „{1}” i „{2}” nie są identyczne.", "Namespace_0_has_no_exported_member_1_2694": "Przestrzeń nazw „{0}” nie ma wyeksportowanej składowej „{1}”.", "Namespace_must_be_given_a_name_1437": "Przestrzeń nazw musi mieć nazwę.", "Namespace_name_cannot_be_0_2819": "Przestrzeń nazw nie może mieć nazwy „{0}”.", "Namespaces_are_not_allowed_in_global_script_files_when_0_is_enabled_If_this_file_is_not_intended_to__1280": "Przestrzenie nazw są niedozwolone w globalnych plikach skryptów, gdy włączona jest opcja „{0}”. Je<PERSON>li ten plik nie jest przeznaczony do bycia skryptem globalnym, ustaw parametr „moduleDetection” na wartość „force” lub dodaj pustą instrukcję „export {}”.", "Neither_decorators_nor_modifiers_may_be_applied_to_this_parameters_1433": "Do parametrów „this” nie można stosować dekoratorów ani modyfikatorów.", "No_base_constructor_has_the_specified_number_of_type_arguments_2508": "Żaden z konstruktorów podstawowych nie ma określonej liczby argumentów typu.", "No_constituent_of_type_0_is_callable_2755": "Żadna składowa typu „{0}” nie jest wywoływalna.", "No_constituent_of_type_0_is_constructable_2759": "Żadnej składowej typu „{0}” nie można s<PERSON>st<PERSON>.", "No_index_signature_with_a_parameter_of_type_0_was_found_on_type_1_7054": "Nie znaleziono żadnej sygnatury indeksu z parametrem typu „{0}” w typie „{1}”.", "No_inputs_were_found_in_config_file_0_Specified_include_paths_were_1_and_exclude_paths_were_2_18003": "Nie można znaleźć danych wejściowych w pliku konfiguracji „{0}”. <PERSON><PERSON><PERSON><PERSON> ścieżki „include” to „{1}”, a „exclude” to „{2}”.", "No_longer_supported_In_early_versions_manually_set_the_text_encoding_for_reading_files_6608": "Nie jest już obsługiwane. W starszych wersjach umożliwia ręczne konfigurowanie kodowania tekstu dla odczytywania plików.", "No_overload_expects_0_arguments_but_overloads_do_exist_that_expect_either_1_or_2_arguments_2575": "Żadne przeciążenie nie oczekuje {0} argumentów, ale istnieją przec<PERSON>, kt<PERSON><PERSON> oczekuj<PERSON> {1} lub {2} argumentów.", "No_overload_expects_0_type_arguments_but_overloads_do_exist_that_expect_either_1_or_2_type_arguments_2743": "Żadne przeciążenie nie oczekuje {0} argumentów typu, ale istnieją przeci<PERSON>, kt<PERSON><PERSON> oczekują {1} lub {2} argumentów typu.", "No_overload_matches_this_call_2769": "Żadne przeciążenie nie jest zgodne z tym wywołaniem.", "No_type_could_be_extracted_from_this_type_node_95134": "Nie można było wyodrębnić żadnego typu z tego węzła typu", "No_value_exists_in_scope_for_the_shorthand_property_0_Either_declare_one_or_provide_an_initializer_18004": "Nie istnieje żadna wartość w zakresie dla właściwości skrótowej „{0}”. Zadeklaruj ją lub udostępnij inicjatora.", "Non_abstract_class_0_does_not_implement_inherited_abstract_member_1_from_class_2_2515": "<PERSON><PERSON><PERSON><PERSON><PERSON>a „{0}” nie implementuje odziedziczonej abstrakcyjnej składowej „{1}” z klasy „{2}”.", "Non_abstract_class_0_is_missing_implementations_for_the_following_members_of_1_Colon_2_2654": "W klasie nieabstrakcyjnej „{0}” brakuje implementacji dla następujących składowych elementu „{1}”: {2}.", "Non_abstract_class_0_is_missing_implementations_for_the_following_members_of_1_Colon_2_and_3_more_2655": "W klasie nieabstrakcyjnej „{0}” brakuje implementacji dla następujących składowych elementu „{1}”: {2} i {3} więcej.", "Non_abstract_class_expression_does_not_implement_inherited_abstract_member_0_from_class_1_2653": "Wyrażenie klasy nieabstrakcyjnej nie implementuje odziedziczonej abstrakcyjnej składowej „{0}” z klasy „{1}”.", "Non_abstract_class_expression_is_missing_implementations_for_the_following_members_of_0_Colon_1_2656": "Brak implementacji nieabstrakcyjnych wyrażeń klasy dla następujących składowych elementu „{0}”: {1}.", "Non_abstract_class_expression_is_missing_implementations_for_the_following_members_of_0_Colon_1_and__2650": "W nieabstrakcyjnym wyrażeniu klasy brakuje implementacji dla następujących składowych elementu „{0}”: {1} i {2} więcej.", "Non_null_assertions_can_only_be_used_in_TypeScript_files_8013": "Asercji o wartości innej niż null można używać tylko w plikach TypeScript.", "Non_relative_paths_are_not_allowed_when_baseUrl_is_not_set_Did_you_forget_a_leading_Slash_5090": "Ścieżki inne niż względne nie są dozwolone, gdy nie jest ustawiona wartość „baseUrl”. Czy nie zostały pominięte początkowe znaki „./”?", "Non_simple_parameter_declared_here_1348": "W tym miej<PERSON> z<PERSON>kla<PERSON> parametr inny niż prosty.", "Not_all_code_paths_return_a_value_7030": "Nie wszystkie ścieżki w kodzie zwracają wartość.", "Not_all_constituents_of_type_0_are_callable_2756": "Nie wszystkie składowe typu „{0}” są wywoływalne.", "Not_all_constituents_of_type_0_are_constructable_2760": "Nie wszystkie składowe typu „{0}” można skonstruować.", "Numbers_out_of_order_in_quantifier_1506": "Liczby poza kolejnością w kwantyfikatorze.", "Numeric_literals_with_absolute_values_equal_to_2_53_or_greater_are_too_large_to_be_represented_accur_80008": "Literały liczbowe z wartościami bezwzględnymi równymi 2^53 lub większymi są zbyt duże, aby mogły być dokładnie reprezentowane jako liczby całkowite.", "Numeric_separators_are_not_allowed_here_6188": "Separatory liczbowe nie są dozwolone w tym mi<PERSON>.", "Object_is_of_type_unknown_2571": "Obiekt jest typu „nieznany”.", "Object_is_possibly_null_2531": "Obiekt ma prawdopodobnie wartość „null”.", "Object_is_possibly_null_or_undefined_2533": "Obiekt ma prawdopodobnie wartość „null” lub „undefined”.", "Object_is_possibly_undefined_2532": "Obiekt ma prawdopodobnie wartość „undefined”.", "Object_literal_may_only_specify_known_properties_and_0_does_not_exist_in_type_1_2353": "Dla literału obiektu można określić tylko znane w<PERSON><PERSON>ci, a właściwość „{0}” nie istnieje w typie „{1}”.", "Object_literal_may_only_specify_known_properties_but_0_does_not_exist_in_type_1_Did_you_mean_to_writ_2561": "Literał obiektu może określać wyłącznie znane właściwości, ale element „{0}” nie istnieje w typie „{1}”. <PERSON><PERSON> chodził<PERSON>i o „{2}”?", "Object_literal_s_property_0_implicitly_has_an_1_type_7018": "<PERSON><PERSON> właściwości „{0}” literału obiektu niejawnie określono typ „{1}”.", "Objects_that_contain_shorthand_properties_can_t_be_inferred_with_isolatedDeclarations_9016": "Obiektów zawierających właściwości skrótowe nie można wywnioskować za pomocą wyrażenia --isolatedDeclarations.", "Objects_that_contain_spread_assignments_can_t_be_inferred_with_isolatedDeclarations_9015": "Obiektów zawierających przypisania rozproszone nie można wywnioskować za pomocą parametru --isolatedDeclarations.", "Octal_digit_expected_1178": "Oczekiwano cyfry ó<PERSON>mkowej.", "Octal_escape_sequences_and_backreferences_are_not_allowed_in_a_character_class_If_this_was_intended__1536": "Ósemkowe sekwencje ucieczki i odwołania wsteczne nie są dozwolone w klasie znaków. <PERSON><PERSON><PERSON> było to zamierzone jako sekwencja ucieczki, uż<PERSON>j składni „{0}”.", "Octal_escape_sequences_are_not_allowed_Use_the_syntax_0_1487": "Ósemkowe sekwencje ucieczki są niedozwolone. Użyj składni „{0}”.", "Octal_literals_are_not_allowed_Use_the_syntax_0_1121": "Literały ósemkowe są niedozwolone. Użyj składni „{0}”.", "One_value_of_0_1_is_the_string_2_and_the_other_is_assumed_to_be_an_unknown_numeric_value_4126": "Jedn<PERSON> z wartości „{0}.{1}” jest ciąg „{2}”, a druga przyjmuje się, że jest nieznaną wartością liczbową.", "Only_a_single_variable_declaration_is_allowed_in_a_for_in_statement_1091": "W instrukcji „for...in” jest dozwolona tylko pojedyncza deklaracja zmiennej.", "Only_a_single_variable_declaration_is_allowed_in_a_for_of_statement_1188": "W instrukcji „for...of” jest dozwolona tylko pojedyncza deklaracja zmiennej.", "Only_a_void_function_can_be_called_with_the_new_keyword_2350": "Tylko funkcja typu void może być wywoływana za pomocą słowa kluczowego „new”.", "Only_ambient_modules_can_use_quoted_names_1035": "Tylko otaczające moduły mogą używać nazw w cudzysłowie.", "Only_amd_and_system_modules_are_supported_alongside_0_6082": "<PERSON><PERSON><PERSON> moduły „amd” i „system” są obsługiwane razem z parametrem --{0}.", "Only_const_arrays_can_be_inferred_with_isolatedDeclarations_9017": "Tylko tablice const można wywnioskować za pomocą wyrażenia --isolatedDeclarations.", "Only_emit_d_ts_declaration_files_6014": "Emituj tylko pliki deklaracji „d.ts”.", "Only_output_d_ts_files_and_not_JavaScript_files_6623": "Generuj tylko pliki d.ts, a nie pliki JavaScript.", "Only_public_and_protected_methods_of_the_base_class_are_accessible_via_the_super_keyword_2340": "Tylko publiczne i chronione metody klasy bazowej są dostępne przy użyciu słowa kluczowego „super”.", "Operator_0_cannot_be_applied_to_type_1_2736": "Nie można zastosować operatora „{0}” do typu „{1}”.", "Operator_0_cannot_be_applied_to_types_1_and_2_2365": "Nie można zastosować operatora „{0}” do typów „{1}” i „{2}”.", "Operators_must_not_be_mixed_within_a_character_class_Wrap_it_in_a_nested_class_instead_1519": "Operatory nie mogą być mieszane w klasie znaków. Zamiast tego zawijaj go w zagnieżdżonej klasie.", "Opt_a_project_out_of_multi_project_reference_checking_when_editing_6619": "Rezygnacja ze sprawdzania odwołań do wielu projektów podczas edytowania projektu.", "Option_0_1_has_been_removed_Please_remove_it_from_your_configuration_5108": "<PERSON><PERSON><PERSON> „{0}={1}” została usunięta. Usuń go z konfiguracji.", "Option_0_1_is_deprecated_and_will_stop_functioning_in_TypeScript_2_Specify_compilerOption_ignoreDepr_5107": "<PERSON><PERSON><PERSON> „{0}={1}” jest przestarzała i przestanie działać w języku TypeScript {2}. Określ parametr compilerOption „ignoreDeprecations”: „{3}”, aby wycis<PERSON><PERSON> ten błąd.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_false_or_null_on_command_line_6230": "<PERSON><PERSON><PERSON><PERSON> „{0}” można określić tylko w pliku „tsconfig.json” albo ustawić na wartość „false” lub „null” w wierszu polecenia.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_null_on_command_line_6064": "<PERSON><PERSON><PERSON><PERSON> „{0}” można określić tylko w pliku „tsconfig.json” albo ustawić na wartość „null” w wierszu polecenia.", "Option_0_can_only_be_specified_on_command_line_6266": "<PERSON><PERSON><PERSON><PERSON> „{0}” można określić tylko w wierszu polecenia.", "Option_0_can_only_be_used_when_either_option_inlineSourceMap_or_option_sourceMap_is_provided_5051": "<PERSON><PERSON><PERSON> „{0}” może być używana tylko w przypadku podania opcji „--inlineSourceMap” lub „--sourceMap”.", "Option_0_can_only_be_used_when_moduleResolution_is_set_to_node16_nodenext_or_bundler_5098": "Opcji „{0}” można używać tylko w<PERSON>y, gdy parametr „moduleResolution” ma warto<PERSON>ć „node16”, „nodenext” lub „bundler”.", "Option_0_can_only_be_used_when_module_is_set_to_preserve_or_to_es2015_or_later_5095": "Opcji „{0}” można używać tylko wtedy, gdy element „module” ma wartoś<PERSON> „preserve” lub „es2015” lub nowsz<PERSON>.", "Option_0_cannot_be_specified_when_option_jsx_is_1_5089": "<PERSON><PERSON> mo<PERSON><PERSON> opcji „{0}”, je<PERSON><PERSON> opcja „jsx” ma warto<PERSON> „{1}”.", "Option_0_cannot_be_specified_with_option_1_5053": "<PERSON><PERSON><PERSON> „{0}” nie można określić razem z opcją „{1}”.", "Option_0_cannot_be_specified_without_specifying_option_1_5052": "<PERSON><PERSON><PERSON> „{0}” nie można określi<PERSON> bez opcji „{1}”.", "Option_0_cannot_be_specified_without_specifying_option_1_or_option_2_5069": "<PERSON><PERSON><PERSON> „{0}” nie można określi<PERSON> bez opcji „{1}” lub opcji „{2}”.", "Option_0_has_been_removed_Please_remove_it_from_your_configuration_5102": "<PERSON><PERSON><PERSON> „{0}” została usunięta. Usuń go z konfiguracji.", "Option_0_is_deprecated_and_will_stop_functioning_in_TypeScript_1_Specify_compilerOption_ignoreDeprec_5101": "<PERSON><PERSON><PERSON> „{0}” jest przestarzała i przestanie działać w języku TypeScript {1}. Określ parametr compilerOption „ignoreDeprecations”: „{2}”, aby wyciszyć ten błąd.", "Option_0_is_redundant_and_cannot_be_specified_with_option_1_5104": "<PERSON><PERSON><PERSON> „{0}\" jest nadmiarowa i nie można jej okre<PERSON> za pomocą opcji „{1}”.", "Option_allowImportingTsExtensions_can_only_be_used_when_either_noEmit_or_emitDeclarationOnly_is_set_5096": "Opcji „allowImportingTsExtensions” można używać tylko w<PERSON>y, gdy jest ustawi<PERSON> opcja „noEmit” lub „emitDeclarationOnly”.", "Option_build_must_be_the_first_command_line_argument_6369": "<PERSON><PERSON><PERSON> „--build” musi być pierwszym argumentem wiersza polecenia.", "Option_incremental_can_only_be_specified_using_tsconfig_emitting_to_single_file_or_when_option_tsBui_5074": "Opcj<PERSON> \"--incremental\" można określić tylko za pomocą pliku tsconfig, emitując do pojedynczego pliku lub gdy określono opcję \"--tsBuildInfoFile\".", "Option_isolatedModules_can_only_be_used_when_either_option_module_is_provided_or_option_target_is_ES_5047": "Opcji „isolatedModules” można użyć tylko wtedy, gdy podano opcję „--module” lub opcja „target” określa cel „ES2015” lub wyższy.", "Option_moduleResolution_must_be_set_to_0_or_left_unspecified_when_option_module_is_set_to_1_5109": "<PERSON><PERSON><PERSON> „moduleResolution” musi być ustawiona na wartość „{0}” (lub pozostać nieokreślona), gdy op<PERSON><PERSON> „module” jest ustawiona na „{1}”.", "Option_module_must_be_set_to_0_when_option_moduleResolution_is_set_to_1_5110": "<PERSON><PERSON><PERSON> „module” musi mie<PERSON> warto<PERSON> „{0}”, gdy opcja „moduleResolution” ma wartość „{1}”.", "Option_preserveConstEnums_cannot_be_disabled_when_0_is_enabled_5091": "<PERSON><PERSON>ja „preserveConstEnums” nie może być wyłączona, gdy opcja „{0}” jest włączona.", "Option_project_cannot_be_mixed_with_source_files_on_a_command_line_5042": "Nie można mieszać opcji „project” z plikami źródłowymi w wierszu polecenia.", "Option_resolveJsonModule_cannot_be_specified_when_moduleResolution_is_set_to_classic_5070": "<PERSON>e można <PERSON> opcji „--resolveJsonModule”, gdy parametr „moduleResolution” ma wartość „classic”.", "Option_resolveJsonModule_cannot_be_specified_when_module_is_set_to_none_system_or_umd_5071": "<PERSON>e można ok<PERSON> opcji „--resolveJsonModule”, gdy parametr „module” ma warto<PERSON> „none”, „system” lub „umd”.", "Option_tsBuildInfoFile_cannot_be_specified_without_specifying_option_incremental_or_composite_or_if__5111": "Nie można ok<PERSON> opcji „tsBuildInfoFile” bez określenia opcji „incremental” lub „composite” albo jeśli nie jest uruchomiona opcja „tsc -b”.", "Option_verbatimModuleSyntax_cannot_be_used_when_module_is_set_to_UMD_AMD_or_System_5105": "Nie można użyć opcji „verbatimModuleSyntax”, gdy parametr „module” ma wartość „UMD”, „AMD” lub „System”.", "Options_0_and_1_cannot_be_combined_6370": "<PERSON>e można połącz<PERSON>ć opcji „{0}” i „{1}”.", "Options_Colon_6027": "Opcje:", "Output_Formatting_6256": "Formatowanie danych wyjściowych", "Output_compiler_performance_information_after_building_6615": "Informacje o wydajności kompilatora danych wyjściowych po skompilowaniu.", "Output_directory_for_generated_declaration_files_6166": "Katalog wyjściowy dla wygenerowanych plików deklaracji.", "Output_file_0_has_not_been_built_from_source_file_1_6305": "Plik wyjściowy „{0}” nie został utworzony na podstawie pliku źródłowego „{1}”.", "Output_from_referenced_project_0_included_because_1_specified_1411": "Dane wyjściowe z przywoływanego projektu „{0}” zostały dołączone, ponieważ określono element „{1}”", "Output_from_referenced_project_0_included_because_module_is_specified_as_none_1412": "Dane wyjściowe z przywoływanego projektu „{0}” zostały dołączone, poniew<PERSON>ż okre<PERSON><PERSON> war<PERSON> „none” dla opcji „--module”", "Output_more_detailed_compiler_performance_information_after_building_6632": "Podaj bardziej szczegółowe informacje o wydajności kompilatora po zakończeniu kompilacji.", "Overload_0_of_1_2_gave_the_following_error_2772": "Przeciążenie {0} z {1}, „{2}”, zwróciło następujący błąd.", "Overload_signatures_must_all_be_abstract_or_non_abstract_2512": "Wszystkie sygnatury przeciążeń muszą być abstrakcyjne lub nieabstrakcyjne.", "Overload_signatures_must_all_be_ambient_or_non_ambient_2384": "Wszystkie sygnatury przeciążeń muszą być otaczającymi sygnaturami lub żadna nie może być otaczającą sygnaturą.", "Overload_signatures_must_all_be_exported_or_non_exported_2383": "Wszystkie sygnatury przeciążeń muszą być wyeksportowane lub żadna nie może być wyeksportowana.", "Overload_signatures_must_all_be_optional_or_required_2386": "Wszystkie sygnatury przeciążeń muszą być opcjonalne lub wymagane.", "Overload_signatures_must_all_be_public_private_or_protected_2385": "Wszystkie sygnatury przeciążeń muszą by<PERSON>, prywatne lub chronione.", "Parameter_0_cannot_reference_identifier_1_declared_after_it_2373": "Parametr „{0}” nie może przywoływać identyfikatora „{1}” zadeklarowanego po nim.", "Parameter_0_cannot_reference_itself_2372": "Parametr „{0}” nie może odwoływać się do siebie samego.", "Parameter_0_implicitly_has_an_1_type_7006": "<PERSON><PERSON> parametru „{0}” niejawnie określono typ „{1}”.", "Parameter_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7044": "Parametr „{0}” niejawnie ma typ „{1}”, ale lepszy typ można wywnioskować na podstawie użycia.", "Parameter_0_is_not_in_the_same_position_as_parameter_1_1227": "Parametr „{0}” nie znajduje się w tym samym położeniu co parametr „{1}”.", "Parameter_0_of_accessor_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4108": "Parametr „{0}” metody dostę<PERSON> ma nazwę „{1}” z modułu zewnętrznego „{2}” lub używa tej nazwy, ale nie można go nazwać.", "Parameter_0_of_accessor_has_or_is_using_name_1_from_private_module_2_4107": "Parametr „{0}” metody dostę<PERSON> ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Parameter_0_of_accessor_has_or_is_using_private_name_1_4106": "Parametr „{0}” metody dostępu ma nazwę prywatną „{1}” lub używa tej nazwy.", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4066": "Parametr „{0}” sygnatury wywołania z wyeksportowanego interfejsu ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4067": "Parametr „{0}” sygnatury wywołania z wyeksportowanego interfejsu ma nazwę prywatną „{1}” lub używa tej nazwy.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_can_4061": "Parametr „{0}” konstruktora z wyeksportowanej klasy ma nazwę „{1}” z modułu zewnętrznego {2} lub używa tej nazwy, ale nie można go nazwać.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_private_module_2_4062": "Parametr „{0}” konstruktora z wyeksportowanej klasy ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_private_name_1_4063": "Parametr „{0}” konstruktora z wyeksportowanej klasy ma nazwę prywatną „{1}” lub używa tej nazwy.", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_name_1_from_private_mod_4064": "Parametr „{0}” sygnatury konstruktora z wyeksportowanego interfejsu ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4065": "Parametr „{0}” sygnatury konstruktora z wyeksportowanego interfejsu ma nazwę prywatną „{1}” lub używa tej nazwy.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4076": "Parametr „{0}” wyeksportowanej funkcji ma nazwę „{1}” z modułu zewnętrznego {2} lub używa tej nazwy, ale nie można go nazwać.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_private_module_2_4077": "Parametr „{0}” wyeksportowanej funkcji ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Parameter_0_of_exported_function_has_or_is_using_private_name_1_4078": "Parametr „{0}” wyeksportowanej funkcji ma nazwę prywatną „{1}” lub używa tej nazwy.", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4091": "Parametr „{0}” sygnatury indeksu z wyeksportowanego interfejsu ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_private_name_1_4092": "Parametr „{0}” sygnatury indeksu z wyeksportowanego interfejsu ma nazwę prywatną „{1}” lub używa tej nazwy.", "Parameter_0_of_method_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4074": "Parametr „{0}” metody z wyeksportowanego interfejsu ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4075": "Parametr „{0}” metody z wyeksportowanego interfejsu ma nazwę prywatną „{1}” lub używa tej nazwy.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_c_4071": "Parametr „{0}” metody publicznej z wyeksportowanej klasy ma nazwę „{1}” z modułu zewnętrznego {2} lub używa tej nazwy, ale nie można go nazwać.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4072": "Parametr „{0}” metody publicznej z wyeksportowanej klasy ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4073": "Parametr „{0}” metody publicznej z wyeksportowanej klasy ma nazwę prywatną „{1}” lub używa tej nazwy.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_external_module__4068": "Parametr „{0}” publicznej metody statycznej z wyeksportowanej klasy ma nazwę „{1}” z modułu zewnętrznego {2} lub używa tej nazwy, ale nie można go nazwać.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4069": "Parametr „{0}” publicznej metody statycznej z wyeksportowanej klasy ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4070": "Parametr „{0}” publicznej metody statycznej z wyeksportowanej klasy ma nazwę prywatną „{1}” lub używa tej nazwy.", "Parameter_cannot_have_question_mark_and_initializer_1015": "Parametr nie może mieć znaku zapytania i inicjatora.", "Parameter_declaration_expected_1138": "Oczekiwano <PERSON>klaracji parametru.", "Parameter_has_a_name_but_no_type_Did_you_mean_0_Colon_1_7051": "Parametr ma nazwę, ale nie ma typu. <PERSON><PERSON>ł<PERSON> o „{0}: {1}”?", "Parameter_modifiers_can_only_be_used_in_TypeScript_files_8012": "Modyfikatorów parametrów można używać tylko w plikach TypeScript.", "Parameter_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9011": "Parametr musi mieć jawną adnotację typu z wyrażeniem --isolatedDeclarations.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4036": "Typ parametru publicznej metody us<PERSON>j „{0}” z wyeksportowanej klasy ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_private_name_1_4037": "Typ parametru publicznej metody us<PERSON>j „{0}” z wyeksportowanej klasy ma nazwę prywatną „{1}” lub używa tej nazwy.", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_name_1_from_private_mod_4034": "Typ parametru publicznej statycznej metody ustawiającej „{0}” z wyeksportowanej klasy ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_private_name_1_4035": "Typ parametru publicznej statycznej metody ustawiającej „{0}” z wyeksportowanej klasy ma nazwę prywatną „{1}” lub używa tej nazwy.", "Parse_in_strict_mode_and_emit_use_strict_for_each_source_file_6141": "Analizuj w trybie z ograniczeniami i emituj ciąg „use strict” dla każdego pliku źródłowego.", "Part_of_files_list_in_tsconfig_json_1409": "<PERSON><PERSON><PERSON><PERSON><PERSON> listy „files” w pliku tsconfig.json", "Pattern_0_can_have_at_most_one_Asterisk_character_5061": "<PERSON><PERSON><PERSON><PERSON> „{0}” może zawierać maksymalnie jeden znak „*”.", "Performance_timings_for_diagnostics_or_extendedDiagnostics_are_not_available_in_this_session_A_nativ_6386": "Chronometraż wydajności dla opcji „--diagnostics” lub „--extendedDiagnostics” nie jest dostępny w tej sesji. Nie można było znaleźć natywnej implementacji interfejsu Web Performance API.", "Platform_specific_6912": "Przeznaczone dla platformy", "Prefix_0_with_an_underscore_90025": "Poprzedzaj elementy „{0}” znakiem podkreślenia", "Prefix_all_incorrect_property_declarations_with_declare_95095": "Dodaj prefiks „declare” do wszystkich niepoprawnych deklaracji właściwości", "Prefix_all_unused_declarations_with_where_possible_95025": "<PERSON><PERSON><PERSON> to możliwe, poprzedź wszystkie nieużywane deklaracje znakiem „_”", "Prefix_with_declare_95094": "<PERSON><PERSON><PERSON> prefiks „declare”", "Preserve_unused_imported_values_in_the_JavaScript_output_that_would_otherwise_be_removed_1449": "Zachowaj nieużywane zaimportowane wartości w wyjściu JavaScript, które w przeciwnym razie mogłyby zostać usunięte.", "Print_all_of_the_files_read_during_the_compilation_6653": "Wyświetlaj wszystkie pliki odczytywane podczas kompilacji.", "Print_files_read_during_the_compilation_including_why_it_was_included_6631": "Wyświetlaj pliki odczytywane podczas kompilacji, włącznie z przyczyną dołączenia.", "Print_names_of_files_and_the_reason_they_are_part_of_the_compilation_6505": "Wyświetl nazwy plików i przyczyny, dla których są częścią kompilacji.", "Print_names_of_files_part_of_the_compilation_6155": "Drukuj nazwy plików będących częścią kompilacji.", "Print_names_of_files_that_are_part_of_the_compilation_and_then_stop_processing_6503": "Drukuj nazwy plików będących częścią kompilacji, a następnie zatrzymaj przetwarzanie.", "Print_names_of_generated_files_part_of_the_compilation_6154": "Drukuj nazwy wygenerowanych plików będących częścią kompilacji.", "Print_the_compiler_s_version_6019": "<PERSON><PERSON><PERSON>z wersję kompilatora.", "Print_the_final_configuration_instead_of_building_1350": "Wydrukuj końcową konfigurację zamiast kompilowania.", "Print_the_names_of_emitted_files_after_a_compilation_6652": "Wyświetlaj nazwy wyemitowanych plików po kompilacji.", "Print_this_message_6017": "Wypisz ten komunikat.", "Private_accessor_was_defined_without_a_getter_2806": "Prywatna metoda dostępu została zdefiniowana bez metody pobierającej.", "Private_field_0_must_be_declared_in_an_enclosing_class_1111": "Pole prywatne „{0}” musi być zadeklarowane w otaczającej klasie.", "Private_identifiers_are_not_allowed_in_variable_declarations_18029": "Identyfikatory prywatne są niedozwolone w deklaracjach zmiennych.", "Private_identifiers_are_not_allowed_outside_class_bodies_18016": "Identyfikatory prywatne są niedozwolone poza treściami klasy.", "Private_identifiers_are_only_allowed_in_class_bodies_and_may_only_be_used_as_part_of_a_class_member__1451": "Identyfikatory prywatne są dozwolone tylko w treściach klasy i mogą być używane tylko jako część deklaracji członka klasy, dostępu do właściwości lub po lewej stronie wyrażenia „in”", "Private_identifiers_are_only_available_when_targeting_ECMAScript_2015_and_higher_18028": "Identyfikatory prywatne są dostępne tylko w<PERSON>y, gdy jest używany język ECMAScript 2015 lub nowszy.", "Private_identifiers_cannot_be_used_as_parameters_18009": "Identyfikatory p<PERSON>watne nie mogą być używane jako parametry.", "Private_or_protected_member_0_cannot_be_accessed_on_a_type_parameter_4105": "Nie można uzyskać dostępu do prywatnego lub chronionego elementu składowego „{0}” w parametrze typu.", "Project_0_can_t_be_built_because_its_dependency_1_has_errors_6363": "Project '{0}' can't be built because its dependency '{1}' has errors", "Project_0_can_t_be_built_because_its_dependency_1_was_not_built_6383": "Project '{0}' can't be built because its dependency '{1}' was not built", "Project_0_is_being_forcibly_rebuilt_6388": "Trwa wymuszone odbudowanie projektu „{0}”", "Project_0_is_out_of_date_because_1_6420": "Projekt „{0}” jest ni<PERSON>, ponieważ {1}.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_file_2_was_root_file_of_compilation_6412": "Projekt „{0}” jest nieak<PERSON><PERSON>, poni<PERSON><PERSON>ż plik buildinfo „{1}” wskazuje, że plik „{2}” był plikiem głównym kompilacji, ale już nie jest.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_program_needs_to_report_errors_6419": "Projekt „{0}” jest ni<PERSON>, poniew<PERSON>ż plik buildinfo „{1}” wskazuje, że program musi zgłaszać błędy.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_some_of_the_changes_were_not_emitte_6399": "Projekt „{0}” jest ni<PERSON>, poni<PERSON><PERSON><PERSON> plik buildinfo „{1}” wska<PERSON>je, że niektóre zmiany nie zostały wyemitowane", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_there_is_change_in_compilerOptions_6406": "Projekt „{0}” jest ni<PERSON>, poniew<PERSON>ż plik buildinfo „{1}” wska<PERSON>je, że nastąpiła zmiana w parametrze compilerOptions", "Project_0_is_out_of_date_because_its_dependency_1_is_out_of_date_6353": "Projekt „{0}” jest nieaktualny, poni<PERSON><PERSON><PERSON> jego z<PERSON> „{1}” jest nieaktualna", "Project_0_is_out_of_date_because_output_1_is_older_than_input_2_6350": "Projekt „{0}” jest ni<PERSON>, poni<PERSON><PERSON><PERSON> dane wyjściowe „{1}” są starsze niż dane wejściowe „{2}”", "Project_0_is_out_of_date_because_output_file_1_does_not_exist_6352": "Projekt „{0}” jest ni<PERSON>, ponieważ plik wyjściowy „{1}” nie istnieje", "Project_0_is_out_of_date_because_output_for_it_was_generated_with_version_1_that_differs_with_curren_6381": "Projekt „{0}” jest nieak<PERSON>al<PERSON>, poni<PERSON><PERSON><PERSON> jego dane wyjściowe zostały wygenerowane w wersji „{1}”, która różni się od bieżącej wersji „{2}”", "Project_0_is_out_of_date_because_there_was_error_reading_file_1_6401": "Projekt „{0}” jest ni<PERSON>, poni<PERSON><PERSON><PERSON> wyst<PERSON><PERSON><PERSON> błąd podczas odczytywania pliku „{1}”", "Project_0_is_up_to_date_6361": "Projekt „{0}” jest aktualny", "Project_0_is_up_to_date_because_newest_input_1_is_older_than_output_2_6351": "Projekt „{0}” jest aktual<PERSON>, poni<PERSON><PERSON><PERSON> najnowsze dane wejściowe „{1}” są starsze niż dane wyjściowe „{2}”", "Project_0_is_up_to_date_but_needs_to_update_timestamps_of_output_files_that_are_older_than_input_fil_6400": "Projekt „{0}” jest a<PERSON><PERSON><PERSON>, ale musi zaktualizować sygnatury czasowe plików wyjściowych, kt<PERSON>re są starsze niż pliki wejściowe", "Project_0_is_up_to_date_with_d_ts_files_from_its_dependencies_6354": "Projekt „{0}” jest aktualny z plikami .d.ts z jego zależności", "Project_references_may_not_form_a_circular_graph_Cycle_detected_Colon_0_6202": "Odwołania do projektu nie mogą tworzyć grafu kołowego. Wykryto cykl: {0}", "Projects_6255": "Projekty", "Projects_in_this_build_Colon_0_6355": "Projekty w tej kompilacji: {0}", "Properties_with_the_accessor_modifier_are_only_available_when_targeting_ECMAScript_2015_and_higher_18045": "Właściwości z modyfikatorem „accessor” są dostępne tylko w przypadku określania wartości docelowej ECMAScript 2015 lub nowszej.", "Property_0_cannot_have_an_initializer_because_it_is_marked_abstract_1267": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> \"{0}\" nie może mieć inicjatora, poniew<PERSON>ż jest oznaczona jako abstrakcyjna.", "Property_0_comes_from_an_index_signature_so_it_must_be_accessed_with_0_4111": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” pochodzi z sygnatury indeksu, dlatego należy uzyskiwać do niej dostęp za pomocą elementu [„{0}”].", "Property_0_does_not_exist_on_type_1_2339": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie istnieje w typie „{1}”.", "Property_0_does_not_exist_on_type_1_Did_you_mean_2_2551": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie istnieje w typie „{1}”. <PERSON><PERSON> chodziło <PERSON> o „{2}”?", "Property_0_does_not_exist_on_type_1_Did_you_mean_to_access_the_static_member_2_instead_2576": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie istnieje w typie „{1}”. <PERSON><PERSON> chodziło o uzyskanie dostępu do statycznego elementu członkowskiego „{2}”?", "Property_0_does_not_exist_on_type_1_Do_you_need_to_change_your_target_library_Try_changing_the_lib_c_2550": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> \"{0}\" nie istnieje w typie \"{1}\". <PERSON><PERSON> ch<PERSON>z zmienić bibliotekę docelową? Spróbuj zmienić opcję kompilatora \"lib\" na \"{2}\" lub nowszą.", "Property_0_does_not_exist_on_type_1_Try_changing_the_lib_compiler_option_to_include_dom_2812": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> \"{0}\" nie istnieje w typie \"{1}\". Spróbuj zmienić opcję kompilatora \"lib\", aby u<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> parametr \"dom\".", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_a_class_static_block_2817": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> \"{0}\" nie ma inicjatora i nie jest zdecydowanie przypisana w bloku statycznym klasy.", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_the_constructor_2564": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie ma inicjatora i nie jest na pewno przypisana w konstruktorze.", "Property_0_implicitly_has_type_any_because_its_get_accessor_lacks_a_return_type_annotation_7033": "<PERSON><PERSON> właściwości „{0}” niejawnie określono typ „any”, ponieważ jego metoda dostępu „get” nie ma adnotacji zwracanego typu.", "Property_0_implicitly_has_type_any_because_its_set_accessor_lacks_a_parameter_type_annotation_7032": "<PERSON><PERSON> właści<PERSON>ści „{0}” niejawnie określono typ „any”, ponieważ jego metoda dostępu „set” nie ma adnotacji typu parametru.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_get_accessor_may_be_inferred_from_usage_7048": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” niejawnie ma typ „any”, ale lepszy typ dla jej metody dostępu get można wywnioskować na podstawie użycia.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_set_accessor_may_be_inferred_from_usage_7049": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” niejawnie ma typ „any”, ale lepszy typ dla jej metody dost<PERSON> set można wywnioskować na podstawie użycia.", "Property_0_in_type_1_is_not_assignable_to_the_same_property_in_base_type_2_2416": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” w typie „{1}” nie można przypisać do tej samej właściwości w typie podstawowym „{2}”.", "Property_0_in_type_1_is_not_assignable_to_type_2_2603": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” w typie „{1}” nie można przypisać do typu „{2}”.", "Property_0_in_type_1_refers_to_a_different_member_that_cannot_be_accessed_from_within_type_2_18015": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” w typie „{1}” odwołuje się do innego elementu członkowskiego, do którego nie można uzyskać dostępu z typu „{2}”.", "Property_0_is_declared_but_its_value_is_never_read_6138": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” jest <PERSON><PERSON><PERSON><PERSON>, ale jej warto<PERSON>ć nie jest nigdy odczytywana.", "Property_0_is_incompatible_with_index_signature_2530": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” jest niezgodna z sygnaturą indeksu.", "Property_0_is_missing_in_type_1_2324": "W typie „{1}” brakuje właściwości „{0}”.", "Property_0_is_missing_in_type_1_but_required_in_type_2_2741": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” brakuje w typie „{1}”, ale jest wymagana w typie „{2}”.", "Property_0_is_not_accessible_outside_class_1_because_it_has_a_private_identifier_18013": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie jest dostępna poza klasą „{1}”, ponieważ ma identyfikator prywatny.", "Property_0_is_optional_in_type_1_but_required_in_type_2_2327": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” jest opcjonalna w typie „{1}”, ale jest wymagana w typie „{2}”.", "Property_0_is_private_and_only_accessible_within_class_1_2341": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” jest prywatna i dostępna tylko w klasie „{1}”.", "Property_0_is_private_in_type_1_but_not_in_type_2_2325": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” jest prywatna w typie „{1}”, ale nie w typie „{2}”.", "Property_0_is_protected_and_only_accessible_through_an_instance_of_class_1_This_is_an_instance_of_cl_2446": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” jest chroniona i dostępna tylko za pośrednictwem wystąpienia klasy „{1}”. To jest wystąpienie klasy „{2}”.", "Property_0_is_protected_and_only_accessible_within_class_1_and_its_subclasses_2445": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” jest chroniona i dostępna tylko w klasie „{1}” oraz w jej pod<PERSON>.", "Property_0_is_protected_but_type_1_is_not_a_class_derived_from_2_2443": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” jest chroniona, ale typ „{1}” nie jest klasą pochodną elementu „{2}”.", "Property_0_is_protected_in_type_1_but_public_in_type_2_2444": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” jest chroniona w typie „{1}”, ale jest publiczna w typie „{2}”.", "Property_0_is_used_before_being_assigned_2565": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” jest używana przed przypisaniem.", "Property_0_is_used_before_its_initialization_2729": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” jest używana przez jej z<PERSON>.", "Property_0_may_not_exist_on_type_1_Did_you_mean_2_2568": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie istnieje w typie „{1}”. <PERSON><PERSON> chodziło <PERSON> o „{2}”?", "Property_0_of_JSX_spread_attribute_is_not_assignable_to_target_property_2606": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” atrybutu rozkładu JSX nie można przypisać do właściwości docelowej.", "Property_0_of_exported_anonymous_class_type_may_not_be_private_or_protected_4094": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” wyeksportowanego typu klasy anonimowej nie może być prywatna ani chroniona.", "Property_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4032": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” wyeksportowanego interfejsu ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Property_0_of_exported_interface_has_or_is_using_private_name_1_4033": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” wyeksportowanego interfejsu ma nazwę prywatną „{1}” lub używa tej nazwy.", "Property_0_of_type_1_is_not_assignable_to_2_index_type_3_2411": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” typu „{1}” nie można przypisać do typu indeksu „{2}” „{3}”.", "Property_0_was_also_declared_here_2733": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” została również zadeklarowana w tym mi<PERSON>.", "Property_0_will_overwrite_the_base_property_in_1_If_this_is_intentional_add_an_initializer_Otherwise_2612": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” zastąpi właś<PERSON><PERSON><PERSON>ć bazową w elemencie „{1}”. <PERSON><PERSON><PERSON> jest to zamierzone, dodaj inicjator. W przeciwnym razie dodaj modyfikator „declare” lub usuń nadmiarową deklarację.", "Property_assignment_expected_1136": "Oczekiwano przypisania właściwości.", "Property_destructuring_pattern_expected_1180": "Oczekiwano wzorca usuwającego strukturę właś<PERSON>wości.", "Property_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9012": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ć musi mieć jawną adnotację typu z parametrem --isolatedDeclarations.", "Property_or_signature_expected_1131": "Oczekiwano właściwości lub sygnatury.", "Property_value_can_only_be_string_literal_numeric_literal_true_false_null_object_literal_or_array_li_1328": "Wartością właściwości może być jedynie literał ciągu, literał numeryczny, war<PERSON><PERSON><PERSON> „true”, „false” i „null”, literał obiektu i literał tablicy.", "Provide_full_support_for_iterables_in_for_of_spread_and_destructuring_when_targeting_ES5_6179": "Udostępnij pełne wsparcie dla elementów iterowanych w elementach „for-of”, rozpiętości i usuwania, gdy elementem docelowym jest „ES5”.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4098": "Metoda publiczna „{0}” wyeksportowanej klasy ma nazwę „{1}” z modułu zewnętrznego {2} lub używa tej nazwy, ale nie można jej nazwać.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4099": "Metoda publiczna „{0}” wyeksportowanej klasy ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Public_method_0_of_exported_class_has_or_is_using_private_name_1_4100": "Metoda publiczna „{0}” wyeksportowanej klasy ma nazwę prywatną „{1}” lub używa tej nazwy.", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_name_4029": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> publiczna „{0}” wyeksportowanej klasy ma nazwę „{1}” z modułu zewnętrznego {2} lub używa tej nazwy, ale nie można jej nazwać.", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4030": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> publiczna „{0}” wyeksportowanej klasy ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Public_property_0_of_exported_class_has_or_is_using_private_name_1_4031": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> publiczna „{0}” wyeksportowanej klasy ma nazwę prywatną „{1}” lub używa tej nazwy.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_4095": "Publiczna metoda statyczna „{0}” wyeksportowanej klasy ma nazwę „{1}” z modułu zewnętrznego {2} lub używa tej nazwy, ale nie można jej nazwa<PERSON>.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4096": "Publiczna metoda statyczna „{0}” wyeksportowanej klasy ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Public_static_method_0_of_exported_class_has_or_is_using_private_name_1_4097": "Publiczna metoda statyczna „{0}” wyeksportowanej klasy ma nazwę prywatną „{1}” lub używa tej nazwy.", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot__4026": "Publiczna właściwość statyczna „{0}” wyeksportowanej klasy ma nazwę „{1}” z modułu zewnętrznego {2} lub używa tej nazwy, ale nie można jej nazwać.", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4027": "Publiczna właściwość statyczna „{0}” wyeksportowanej klasy ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Public_static_property_0_of_exported_class_has_or_is_using_private_name_1_4028": "Publiczna właściwość statyczna „{0}” wyeksportowanej klasy ma nazwę prywatną „{1}” lub używa tej nazwy.", "Qualified_name_0_is_not_allowed_without_a_leading_param_object_1_8032": "Na<PERSON><PERSON> „{0}” nie jest dozwolona bez wiodącego elementu „@param {object} {1}”.", "Raise_an_error_when_a_function_parameter_isn_t_read_6676": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>, gdy parametr funkcji nie zostanie odczytany.", "Raise_error_on_expressions_and_declarations_with_an_implied_any_type_6052": "Zgłaszaj błąd w przypadku wyrażeń i deklaracji z implikowanym typem „any”.", "Raise_error_on_this_expressions_with_an_implied_any_type_6115": "Zgłaszaj błąd w przypadku wyrażeń „this” z niejawnym typem „any”.", "Range_out_of_order_in_character_class_1517": "Zakres poza kolejnością w klasie znaków.", "Re_exporting_a_type_when_0_is_enabled_requires_using_export_type_1205": "Ponowne eksportowanie typu, gdy element „{0}” jest wł<PERSON>, wymaga użycia elementu „export type”.", "React_components_cannot_include_JSX_namespace_names_2639": "Składniki platformy React nie mogą zawierać nazw przestrzeni rozszerzenia JSX", "Redirect_output_structure_to_the_directory_6006": "Przekieruj strukturę wyjściową do katalogu.", "Reduce_the_number_of_projects_loaded_automatically_by_TypeScript_6617": "Zmniejsz liczbę projektów ładowanych automatycznie przez język TypeScript.", "Referenced_project_0_may_not_disable_emit_6310": "Przywoływany projekt „{0}” nie może wyłączać emisji.", "Referenced_project_0_must_have_setting_composite_Colon_true_6306": "Przywoływany projekt „{0}” musi mieć ustawienie „composite” o wartości true.", "Referenced_via_0_from_file_1_1400": "Przywoływane za pośrednictwem elementu „{0}” z pliku „{1}”", "Relative_import_paths_need_explicit_file_extensions_in_ECMAScript_imports_when_moduleResolution_is_n_2834": "Względne ścieżki importu wymagają jawnych rozszerzeń plików w importach ECMAScript, gdy element „--moduleResolution” ma wartość „node16” lub „nodenext”. Zastanów się nad dodaniem rozszerzenia do ścieżki importu.", "Relative_import_paths_need_explicit_file_extensions_in_ECMAScript_imports_when_moduleResolution_is_n_2835": "Względne ścieżki importu wymagają jawnych rozszerzeń plików w importach ECMAScript, gdy element „--moduleResolution” ma wartość „node16” lub „nodenext”. <PERSON><PERSON> chodziło Ci o „{0}”?", "Remove_a_list_of_directories_from_the_watch_process_6628": "Usuń listę katalogów z procesu obserwacji.", "Remove_a_list_of_files_from_the_watch_mode_s_processing_6629": "Usuń listę plików z przetwarzania w trybie obserwacji.", "Remove_all_unnecessary_override_modifiers_95163": "Usuń wszystkie niepotrzebne modyfikatory „overrides”", "Remove_all_unnecessary_uses_of_await_95087": "Usuń wszystkie niepotrzebne użycia operatora „await”", "Remove_all_unreachable_code_95051": "Usuń cały nieosiągalny kod", "Remove_all_unused_labels_95054": "Usuń wszystkie nieużywane etykiety", "Remove_braces_from_all_arrow_function_bodies_with_relevant_issues_95115": "Usuń nawiasy klamrowe ze wszystkich treści funkcji strzałkowej z odpowiednimi problemami", "Remove_braces_from_arrow_function_95060": "Usuń nawiasy klamrowe z funkcji strzałki", "Remove_braces_from_arrow_function_body_95112": "Usuń nawiasy klamrowe z treści funkcji strzałkowej", "Remove_import_from_0_90005": "Usuń import z „{0}”", "Remove_override_modifier_95161": "<PERSON><PERSON><PERSON> modyfikator „override”", "Remove_parentheses_95126": "Us<PERSON>ń nawiasy", "Remove_template_tag_90011": "Usuń znacznik szablonu", "Remove_the_20mb_cap_on_total_source_code_size_for_JavaScript_files_in_the_TypeScript_language_server_6618": "Usuń ograniczenie 20 MB dotyczące łącznego rozmiaru kodu źródłowego dla plików JavaScript na serwerze języka TypeScript.", "Remove_type_from_import_declaration_from_0_90055": "Usuń element „type” z deklaracji importu z elementu „ {0}”", "Remove_type_from_import_of_0_from_1_90056": "Usuń element „type” z importu elementu „{0}” z „{1}”", "Remove_type_parameters_90012": "Usuń parametry typu", "Remove_unnecessary_await_95086": "Usuń niepotrzebny operator „await”", "Remove_unreachable_code_95050": "Usuń nieosiągalny kod", "Remove_unused_declaration_for_Colon_0_90004": "<PERSON><PERSON><PERSON> nieużywaną de<PERSON>rac<PERSON>ę dla: „{0}”", "Remove_unused_declarations_for_Colon_0_90041": "Usuń nieużywane deklaracje dla: „{0}”", "Remove_unused_destructuring_declaration_90039": "<PERSON><PERSON><PERSON> nieużywaną deklarację usuwania struktury", "Remove_unused_label_95053": "Us<PERSON>ń nieużywaną etykietę", "Remove_variable_statement_90010": "<PERSON><PERSON><PERSON> instrukcję zmiennej", "Rename_param_tag_name_0_to_1_95173": "Zmień nazwę „{0}” tagu „@param” na „{1}”", "Replace_0_with_Promise_1_90036": "Zamień element „{0}” na element „Promise<{1}>”", "Replace_all_unused_infer_with_unknown_90031": "Zamień wszystkie nieużywane elementy „infer” na „unknown”", "Replace_import_with_0_95015": "Zamień import na element „{0}”.", "Replace_infer_0_with_unknown_90030": "Zamień element „infer {0}” na „unknown”", "Report_error_when_not_all_code_paths_in_function_return_a_value_6075": "<PERSON><PERSON><PERSON><PERSON><PERSON> błąd, gdy nie wszystkie ścieżki w kodzie zwracają wartość.", "Report_errors_for_fallthrough_cases_in_switch_statement_6076": "<PERSON><PERSON><PERSON><PERSON>ś błędy dla przepuszczających klauzul case w instrukcji switch.", "Report_errors_in_js_files_8019": "Zgłaszaj błędy w plikach js.", "Report_errors_on_unused_locals_6134": "Raport<PERSON>j błędy dla nieużywanych elementów lokalnych.", "Report_errors_on_unused_parameters_6135": "Raport<PERSON><PERSON> błędy dla nieużywanych parametrów.", "Require_sufficient_annotation_on_exports_so_other_tools_can_trivially_generate_declaration_files_6719": "Wymagaj wystarczającej adnotacji w eksportach, aby inne narzędzia mogły w prosty sposób generować pliki deklaracji.", "Require_undeclared_properties_from_index_signatures_to_use_element_accesses_6717": "<PERSON><PERSON><PERSON><PERSON>, aby niezadeklarowane właściwości z sygnatur indeksów korzystały z dostępów do elementów.", "Required_type_parameters_may_not_follow_optional_type_parameters_2706": "Wymagane parametry typu mogą nie być zgodne z opcjonalnymi parametrami typu.", "Resolution_for_module_0_was_found_in_cache_from_location_1_6147": "Znaleziono rozwiązanie dla modułu „{0}” w pamięci podręcznej z lokalizacji „{1}”.", "Resolution_for_type_reference_directive_0_was_found_in_cache_from_location_1_6241": "Znaleziono rozwiązanie dla dyrektywy odwołania do typu „{0}” w pamięci podręcznej z lokalizacji „{1}”.", "Resolution_of_non_relative_name_failed_trying_with_modern_Node_resolution_features_disabled_to_see_i_6277": "Rozpoznawanie nazwy innej niż względna nie powiodło się; próba wyłączenia nowoczesnych funkcji rozpoznawania węzłów w celu sprawdzenia, czy biblioteka npm wymaga aktualizacji konfiguracji.", "Resolution_of_non_relative_name_failed_trying_with_moduleResolution_bundler_to_see_if_project_may_ne_6279": "Rozpoznawanie nazwy innej niż względna nie powiodło się; próba użycia wyrażenia „--moduleResolution”, aby s<PERSON>, czy projekt może wymagać aktualizacji konfiguracji.", "Resolve_keyof_to_string_valued_property_names_only_no_numbers_or_symbols_6195": "Rozwiązuj elementy „keyof” tylko do nazw właściwości mających jako wartość ciągi (nie liczby czy symbole).", "Resolved_under_condition_0_6414": "Rozwiązano pod warunkiem „{0}”.", "Resolving_in_0_mode_with_conditions_1_6402": "Rozpoznawanie w trybie {0} z warunkami {1}.", "Resolving_module_0_from_1_6086": "======== Rozpoznawanie modułu „{0}” na podstawie „{1}”. ========", "Resolving_module_name_0_relative_to_base_url_1_2_6094": "Rozpoznawanie nazwy modułu „{0}” względem podstawowego adresu URL „{1}” — „{2}”.", "Resolving_real_path_for_0_result_1_6130": "Rozpoznawanie rzeczywistej ścieżki elementu „{0}”, wynik: „{1}”.", "Resolving_type_reference_directive_0_containing_file_1_6242": "======== Rozwiązywanie dyrektywy odwołania do typu „{0}”, zawierającego plik: „{1}”. ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_2_6116": "======== Rozpoznawanie dyrektywy odwołania do typu „{0}”, plik zawierający: „{1}”, katalog główny: „{2}”. ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_not_set_6123": "======== Rozpoznawanie dyrektywy odwołania do typu „{0}”, plik zawierający: „{1}”, katalog główny nie został ustawiony. ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_1_6127": "======== Rozpoznawanie dyrektywy odwołania do typu „{0}”, plik zawierający nie został ustawiony, katalog główny: „{1}”. ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_not_set_6128": "======== Rozpoznawanie dyrektywy odwołania do typu „{0}”, plik zawierający nie został ustawiony, katalog główny nie został ustawiony. ========", "Resolving_type_reference_directive_for_program_that_specifies_custom_typeRoots_skipping_lookup_in_no_6265": "Rozpoznawanie dyrektywy odwołania do typu dla programu, który określa niestandardowe typeRoots, pomijanie wyszukiwania w folderze „node_modules”.", "Resolving_with_primary_search_path_0_6121": "Rozpoznawanie przy użyciu ścieżki wyszukiwania podstawowego „{0}”.", "Rest_parameter_0_implicitly_has_an_any_type_7019": "Dla parametru rest „{0}” niejawnie określono typ „any[]”.", "Rest_parameter_0_implicitly_has_an_any_type_but_a_better_type_may_be_inferred_from_usage_7047": "Parametr rest „{0}” niejawnie ma typ „any[]”, ale lepszy typ można wywnioskować na podstawie użycia.", "Rest_types_may_only_be_created_from_object_types_2700": "Typy rest można tworzyć tylko na podstawie typów obiektu.", "Return_type_annotation_circularly_references_itself_2577": "Adnotacja zwracanego typu cyklicznie odwołuje się do samej siebie.", "Return_type_must_be_inferred_from_a_function_95149": "Zwracany typ musi zostać wywnioskowany na podstawie funkcji", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4046": "Zwracany typ sygnatury wywołania z wyeksportowanego interfejsu ma nazwę „{0}” z modułu prywatnego „{1}” lub używa tej nazwy.", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_private_name_0_4047": "Zwracany typ sygnatury wywołania z wyeksportowanego interfejsu ma nazwę prywatną „{0}” lub używa tej nazwy.", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_name_0_from_private_mod_4044": "Zwracany typ sygnatury konstruktora z wyeksportowanego interfejsu ma nazwę „{0}” z modułu prywatnego „{1}” lub używa tej nazwy.", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_0_4045": "Zwracany typ sygnatury konstruktora z wyeksportowanego interfejsu ma nazwę prywatną „{0}” lub używa tej nazwy.", "Return_type_of_constructor_signature_must_be_assignable_to_the_instance_type_of_the_class_2409": "<PERSON><PERSON> <PERSON><PERSON><PERSON> mo<PERSON><PERSON><PERSON>ść przypisania zwracanego typu sygnatury konstruktora do typu wystąpienia klasy.", "Return_type_of_exported_function_has_or_is_using_name_0_from_external_module_1_but_cannot_be_named_4058": "Zwracany typ wyeksportowanej funkcji ma nazwę „{0}” z modułu zewnętrznego {1} lub używa tej nazwy, ale nie można go nazwać.", "Return_type_of_exported_function_has_or_is_using_name_0_from_private_module_1_4059": "Zwracany typ wyeksportowanej funkcji ma nazwę „{0}” z modułu prywatnego „{1}” lub używa tej nazwy.", "Return_type_of_exported_function_has_or_is_using_private_name_0_4060": "Zwracany typ wyeksportowanej funkcji ma nazwę prywatną „{0}” lub używa tej nazwy.", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4048": "Zwracany typ sygnatury indeksu z wyeksportowanego interfejsu ma nazwę „{0}” z modułu prywatnego „{1}” lub używa tej nazwy.", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_private_name_0_4049": "Zwracany typ sygnatury indeksu z wyeksportowanego interfejsu ma nazwę prywatną „{0}” lub używa tej nazwy.", "Return_type_of_method_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4056": "Zwracany typ metody z wyeksportowanego interfejsu ma nazwę „{0}” z modułu prywatnego „{1}” lub używa tej nazwy.", "Return_type_of_method_from_exported_interface_has_or_is_using_private_name_0_4057": "Zwracany typ metody z wyeksportowanego interfejsu ma nazwę prywatną „{0}” lub używa tej nazwy.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_4041": "Zwracany typ publicznej metody pobierającej „{0}” z wyeksportowanej klasy ma nazwę „{1}” z modułu zewnętrznego {2} lub używa tej nazwy, ale nie można go nazwać.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4042": "Zwracany typ publicznej metody pobierającej „{0}” z wyeksportowanej klasy ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_private_name_1_4043": "Zwracany typ publicznej metody pobierającej „{0}” z wyeksportowanej klasy ma nazwę prywatną „{1}” lub używa tej nazwy.", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_external_module_1_but_c_4053": "Zwracany typ metody publicznej z wyeksportowanej klasy ma nazwę „{0}” z modułu zewnętrznego {1} lub używa tej nazwy, ale nie można go nazwać.", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4054": "Zwracany typ metody publicznej z wyeksportowanej klasy ma nazwę „{0}” z modułu prywatnego „{1}” lub używa tej nazwy.", "Return_type_of_public_method_from_exported_class_has_or_is_using_private_name_0_4055": "Zwracany typ metody publicznej z wyeksportowanej klasy ma nazwę prywatną „{0}” lub używa tej nazwy.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_external_modul_4038": "Zwracany typ publicznej statycznej metody pobiera<PERSON>cej „{0}” z wyeksportowanej klasy ma nazwę „{1}” z modułu zewnętrznego {2} lub używa tej nazwy, ale nie można go nazwać.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_4039": "Zwracany typ publicznej statycznej metody pobierającej „{0}” z wyeksportowanej klasy ma nazwę „{1}” z modułu prywatnego „{2}” lub używa tej nazwy.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_private_name_1_4040": "Zwracany typ publicznej statycznej metody pobierającej „{0}” z wyeksportowanej klasy ma nazwę prywatną „{1}” lub używa tej nazwy.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_external_module__4050": "Zwracany typ publicznej metody statycznej z wyeksportowanej klasy ma nazwę „{0}” z modułu zewnętrznego {1} lub używa tej nazwy, ale nie można go nazwać.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4051": "Zwracany typ publicznej metody statycznej z wyeksportowanej klasy ma nazwę „{0}” z modułu prywatnego „{1}” lub używa tej nazwy.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_private_name_0_4052": "Zwracany typ publicznej metody statycznej z wyeksportowanej klasy ma nazwę prywatną „{0}” lub używa tej nazwy.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_not_resolved_6395": "Ponowne użycie rozwiązania modułu „{0}” z „{1}” w pamięci podręcznej z lokalizacji „{2}” nie zostało rozpoznane.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6393": "Ponowne użycie rozwiązania modułu „{0}” z „{1}” w pamięci podręcznej z lokalizacji „{2}” pomyślnie rozpoznano jako „{3}”.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6394": "Ponowne użycie rozwiązania modułu „{0}” z „{1}” w pamięci podręcznej z lokalizacji „{2}” pomyślnie rozpoznano jako „{3}” z identyfikatorem pakietu „{4}”.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_not_resolved_6389": "Ponowne użycie rozwiązania modułu „{0}” z „{1}” starego programu nie zostało rozpoznane.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_6183": "Ponowne użycie rozwiązania modułu „{0}” z „{1}” starego programu, pomyślnie rozpoznano jako „{2}”.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_with_Package__6184": "Ponowne użycie rozwiązania modułu „{0}” z „{1}” starego programu, pomyślnie rozpoznano jako „{2}” z identyfikatorem pakietu „{3}”.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_not_re_6398": "Ponowne użycie rozpoznawania dyrektywy odwołania typu „{0}” z „{1}” w pamięci podręcznej z lokalizacji „{2}” nie zostało rozpoznane.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6396": "Ponowne użycie rozpoznawania dyrektywy odwołania typu „{0}” z „{1}” w pamięci podręcznej z lokalizacji „{2}” pomyślnie rozpoznano jako „{3}”.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6397": "Ponowne użycie rozpoznawania dyrektywy odwołania typu „{0}” z „{1}” w pamięci podręcznej z lokalizacji „{2}” pomyślnie rozpoznano jako „{3}” z identyfikatorem pakietu „{4}”.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_not_resolved_6392": "Ponowne użycie rozwiązania dyrektywy odwołania typu „{0}” z „{1}” starego programu nie zostało rozpoznane.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6390": "Ponowne użycie rozpoznawania dyrektywy odwołania typu „{0}” z „{1}” starego programu pomyślnie rozpoznano jako „{2}”.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6391": "Ponowne użycie rozpoznawania dyrektywy odwołania typu „{0}” z „{1}” starego programu pomyślnie rozpoznano jako „{2}” o identyfikatorze pakietu „{3}”.", "Rewrite_all_as_indexed_access_types_95034": "Zmień wszystko na indeksowane typy dostępu", "Rewrite_as_the_indexed_access_type_0_90026": "Napisz ponownie jako indeksowany typ dostępu „{0}”", "Right_operand_of_is_unreachable_because_the_left_operand_is_never_nullish_2869": "Prawy operand elementu ?? jest ni<PERSON><PERSON>, poniew<PERSON>ż lewy operand nigdy nie dopuszcza wartości null.", "Root_directory_cannot_be_determined_skipping_primary_search_paths_6122": "Nie można określić katalogu głównego. Pomijanie ścieżek wyszukiwania podstawowego.", "Root_file_specified_for_compilation_1427": "Plik główny określony na potrzeby kompilacji", "STRATEGY_6039": "STRATEGIA", "Save_tsbuildinfo_files_to_allow_for_incremental_compilation_of_projects_6642": "Zapisuj pliki tsbuildinfo, aby um<PERSON><PERSON><PERSON><PERSON>ć przyrostową kompilację projektów.", "Saw_non_matching_condition_0_6405": "Wyświetlono niezgodny warunek „{0}”.", "Scoped_package_detected_looking_in_0_6182": "Wykryto pakiet w zak<PERSON>ie, wyszukiwanie w „{0}”", "Searching_all_ancestor_node_modules_directories_for_fallback_extensions_Colon_0_6418": "Przeszukiwanie wszystkich katalogów nadrzędnych node_modules w poszukiwaniu rozszerzeń rezerwowych: {0}.", "Searching_all_ancestor_node_modules_directories_for_preferred_extensions_Colon_0_6417": "Wyszukiwanie preferowanych rozszerzeń we wszystkich katalogach nadrzędnych node_modules: {0}.", "Selection_is_not_a_valid_statement_or_statements_95155": "Wyb<PERSON>r nie jest prawidłową instrukcją ani instrukcjami", "Selection_is_not_a_valid_type_node_95133": "Wybór nie jest prawidłowym węzłem typu", "Set_the_JavaScript_language_version_for_emitted_JavaScript_and_include_compatible_library_declaratio_6705": "Określ wersję języka JavaScript dla emitowanego kodu JavaScript i dołącz zgodne deklaracje bibliotek.", "Set_the_language_of_the_messaging_from_TypeScript_This_does_not_affect_emit_6654": "Określ język komunikatów z języka TypeScript. Nie wpływa to na emisję.", "Set_the_module_option_in_your_configuration_file_to_0_95099": "Ustaw opcję „module” w pliku konfiguracji na wartość „{0}”", "Set_the_newline_character_for_emitting_files_6659": "Określ znak nowego wiersza dla emisji plików.", "Set_the_target_option_in_your_configuration_file_to_0_95098": "Ustaw opcję „target” w pliku konfiguracji na wartość „{0}”", "Setters_cannot_return_a_value_2408": "<PERSON><PERSON> nie mogą zwracać wartości.", "Show_all_compiler_options_6169": "Pokaż wszystkie opcje kompilatora.", "Show_diagnostic_information_6149": "Pokaż informacje diagnostyczne.", "Show_verbose_diagnostic_information_6150": "Pokaż pełne informacje diagnostyczne.", "Show_what_would_be_built_or_deleted_if_specified_with_clean_6367": "<PERSON><PERSON><PERSON>, co zostanie skompilowane (lub <PERSON><PERSON>, je<PERSON><PERSON> określono opcję „--clean”)", "Signature_0_must_be_a_type_predicate_1224": "Sygnatura „{0}” musi być predykatem typów.", "Signature_declarations_can_only_be_used_in_TypeScript_files_8017": "Deklaracji podpisu można używać tylko w plikach TypeScript.", "Skip_building_downstream_projects_on_error_in_upstream_project_6640": "Pomiń kompilowanie projektów podrzędnych w przypadku błędu w projekcie nadrzędnym.", "Skip_type_checking_all_d_ts_files_6693": "Pomiń sprawdzanie typów dla wszystkich plików d.ts.", "Skip_type_checking_d_ts_files_that_are_included_with_TypeScript_6692": "Pomiń sprawdzanie typów w plikach d.ts dołączanych w kodzie TypeScript.", "Skip_type_checking_of_declaration_files_6012": "Pomiń sprawdzanie typu plików deklaracji.", "Skipping_build_of_project_0_because_its_dependency_1_has_errors_6362": "Skipping build of project '{0}' because its dependency '{1}' has errors", "Skipping_build_of_project_0_because_its_dependency_1_was_not_built_6382": "Skipping build of project '{0}' because its dependency '{1}' was not built", "Skipping_module_0_that_looks_like_an_absolute_URI_target_file_types_Colon_1_6164": "Pomijan<PERSON> modułu „{0}”, który wygląda jak bezwzględny identyfikator URI, docelowe typy plików: {1}.", "Source_from_referenced_project_0_included_because_1_specified_1414": "Źródło z przywoływanego projektu „{0}” zostało dołączone, ponieważ określono element „{1}”", "Source_from_referenced_project_0_included_because_module_is_specified_as_none_1415": "Źródło z przywoływanego projektu „{0}” zostało dołączone, poniew<PERSON>ż okreś<PERSON> war<PERSON> „none” dla opcji „--module”", "Source_has_0_element_s_but_target_allows_only_1_2619": "Liczba elementów w źródle to {0}, ale element docelowy zezwala tylko na {1}.", "Source_has_0_element_s_but_target_requires_1_2618": "Liczba elementów w źródle to {0}, ale element docelowy wymaga {1}.", "Source_provides_no_match_for_required_element_at_position_0_in_target_2623": "Źródło nie udostępnia dopasowania dla wymaganego elementu na pozycji {0} w lokalizacji docelowej.", "Source_provides_no_match_for_variadic_element_at_position_0_in_target_2624": "Źródło nie udostępnia dopasowania dla elementu ze zmienną argumentów na pozycji {0} w lokalizacji docelowej.", "Specify_ECMAScript_target_version_6015": "Określ wersję docelową ECMAScript.", "Specify_JSX_code_generation_6080": "Określ generowanie kodu JSX.", "Specify_a_file_that_bundles_all_outputs_into_one_JavaScript_file_If_declaration_is_true_also_designa_6679": "Określ plik łączący wszystkie dane wyjściowe w jeden plik JavaScript. Jeśli element „declaration” ma wartość true, wyznaczany jest też plik łączący wszystkie dane wyjściowe d.ts.", "Specify_a_list_of_glob_patterns_that_match_files_to_be_included_in_compilation_6641": "Określ listę wzorców globalnych pasujących do plików, które należy uwzględnić w kompilacji.", "Specify_a_list_of_language_service_plugins_to_include_6681": "Określ listę wtyczek usług języka do uwzględnienia.", "Specify_a_set_of_bundled_library_declaration_files_that_describe_the_target_runtime_environment_6651": "Określ zestaw połączonych plików deklaracji bibliotek, które opisują docelowe środowisko uruchomieniowe.", "Specify_a_set_of_entries_that_re_map_imports_to_additional_lookup_locations_6680": "Określ zestaw wpisów, które ponownie mapują operacje importu na dodatkowe lokalizacje wyszukiwania.", "Specify_an_array_of_objects_that_specify_paths_for_projects_Used_in_project_references_6687": "Określ tablicę obiektów określających ścieżki dla projektów. Używane w odwołaniach do projektów.", "Specify_an_output_folder_for_all_emitted_files_6678": "Określ folder wyjściowy dla wszystkich emitowanych plików.", "Specify_emit_Slashchecking_behavior_for_imports_that_are_only_used_for_types_6718": "Określ zachowanie emisji/sprawdzania dla importów, które są używane tylko dla typów.", "Specify_file_to_store_incremental_compilation_information_6380": "Określ plik do przechowywania informacji o kompilacji przyrostowej", "Specify_how_TypeScript_looks_up_a_file_from_a_given_module_specifier_6658": "<PERSON><PERSON><PERSON><PERSON>, jak ję<PERSON>k TypeScript ma wyszukiwać plik z podanego specyfikatora modułu.", "Specify_how_directories_are_watched_on_systems_that_lack_recursive_file_watching_functionality_6714": "Określ sposób obserwacji katalogów w systemach, które nie mają funkcji rekursywnej obserwacji plików.", "Specify_how_the_TypeScript_watch_mode_works_6715": "Określ sposób działania trybu zegarka TypeScript.", "Specify_library_files_to_be_included_in_the_compilation_6079": "Określ pliki biblioteki do uwzględnienia w kompilacji.", "Specify_module_code_generation_6016": "Określ generowanie kodu modułu.", "Specify_module_specifier_used_to_import_the_JSX_factory_functions_when_using_jsx_Colon_react_jsx_Ast_6649": "Określ specyfikator modułów używany do importowania funkcji fabryki JSX w przypadku używania elementów „jsx: react-jsx*”.", "Specify_multiple_folders_that_act_like_Slashnode_modules_Slash_types_6710": "Określ wiele folderów działających jak element „./node_modules/@types”.", "Specify_one_or_more_path_or_node_module_references_to_base_configuration_files_from_which_settings_a_6633": "Określ co najmniej jedną ścieżkę lub odwołanie do modułu platformy Node dotyczące podstawowych plików konfiguracji, z których są dziedziczone ustawienia.", "Specify_options_for_automatic_acquisition_of_declaration_files_6709": "Określ opcje automatycznego pozyskiwania plików deklaracji.", "Specify_strategy_for_creating_a_polling_watch_when_it_fails_to_create_using_file_system_events_Colon_6227": "Określ strategię obserwowania z sondowaniem, gdy nie powiedzie się utworzenie przy użyciu zdarzeń systemu plików: „FixedInterval” (domyślna), „PriorityInterval”, „DynamicPriority”, „FixedChunkSize”.", "Specify_strategy_for_watching_directory_on_platforms_that_don_t_support_recursive_watching_natively__6226": "Określ strategię obserwowania katalogu na platformach, które nie obsługują natywnego obserwowania rekursywnego: „UseFsEvents” (domyślna), „FixedPollingInterval”, „DynamicPriorityPolling”, „FixedChunkSizePolling”.", "Specify_strategy_for_watching_file_Colon_FixedPollingInterval_default_PriorityPollingInterval_Dynami_6225": "Określ strategię obserwowania pliku: „FixedPollingInterval” (domyślna), „PriorityPollingInterval”, „DynamicPriorityPolling”, „FixedChunkSizePolling”, „UseFsEvents”, „UseFsEventsOnParentDirectory”.", "Specify_the_JSX_Fragment_reference_used_for_fragments_when_targeting_React_JSX_emit_e_g_React_Fragme_6648": "Określ odwołanie do fragmentów JSX używane dla fragmentów w przypadku docelowej emisji kodu React JSX, np. „React.Fragment” lub „Fragment”.", "Specify_the_JSX_factory_function_to_use_when_targeting_react_JSX_emit_e_g_React_createElement_or_h_6146": "Określ funkcję fabryki JSX do użycia, gdy elementem docelowym jest emisja elementu JSX „react”, np. „React.createElement” lub „h”.", "Specify_the_JSX_factory_function_used_when_targeting_React_JSX_emit_e_g_React_createElement_or_h_6647": "Określ funkcję fabryki JSX używaną w przypadku docelowej emisji kodu React JSX, na przykład „React.createElement” lub „h”", "Specify_the_JSX_fragment_factory_function_to_use_when_targeting_react_JSX_emit_with_jsxFactory_compi_18034": "Określ funkcję fabryki fragmentów JSX, która ma być używana po ukierunkowaniu na emisję JSX „react” za pomocą opcji kompilatora „jsxFactory”, na przykład „Fragment”.", "Specify_the_base_directory_to_resolve_non_relative_module_names_6607": "Określ katalog podstawowy, aby roz<PERSON>z<PERSON> nie względne nazwy modułów.", "Specify_the_end_of_line_sequence_to_be_used_when_emitting_files_Colon_CRLF_dos_or_LF_unix_6060": "Określ sekwencję końca w<PERSON>za, która ma być używana podczas emitowania plików: „CRLF” (dos) lub „LF” (unix).", "Specify_the_location_where_debugger_should_locate_TypeScript_files_instead_of_source_locations_6004": "Określ lokalizację, w której debuger ma szukać plików TypeScript zamiast szukania w lokalizacjach źródłowych.", "Specify_the_location_where_debugger_should_locate_map_files_instead_of_generated_locations_6655": "Określ lokalizację, w której debuger ma szukać plików map zamiast szukania w wygenerowanych lokalizacjach.", "Specify_the_maximum_folder_depth_used_for_checking_JavaScript_files_from_node_modules_Only_applicabl_6656": "Określ maksymalną głębokość folderów używaną do sprawdzania plików JavaScript z poziomu elementu „node_modules”. Ma to zastosowanie tylko w przypadku użycia opcji „allowJs”.", "Specify_the_module_specifier_to_be_used_to_import_the_jsx_and_jsxs_factory_functions_from_eg_react_6238": "Określ specyfikator modułu, kt<PERSON><PERSON> ma być używany do importowania z funkcji fabryki \"jsx\" i \"jsxs\", na przykład z platformy React", "Specify_the_object_invoked_for_createElement_This_only_applies_when_targeting_react_JSX_emit_6686": "Określ obiekt wywoływany dla elementu „createElement”. Ma to zastosowanie tylko w przypadku docelowej emisji kodu JSX „react”.", "Specify_the_output_directory_for_generated_declaration_files_6613": "Określ katalog wyjściowy dla generowanych plików deklaracji.", "Specify_the_path_to_tsbuildinfo_incremental_compilation_file_6707": "Określ ścieżkę do pliku kompilacji przyrostowej .tsbuildinfo.", "Specify_the_root_directory_of_input_files_Use_to_control_the_output_directory_structure_with_outDir_6058": "Określ katalog główny plików wejściowych. Strukturą katalogów wyjściowych można sterować przy użyciu opcji --outDir.", "Specify_the_root_folder_within_your_source_files_6690": "Określ folder główny w plikach źródłowych.", "Specify_the_root_path_for_debuggers_to_find_the_reference_source_code_6695": "Określ ścieżkę katalogu głównego, w którym debugery będą mogły znaleźć referencyjny kod źródłowy.", "Specify_type_package_names_to_be_included_without_being_referenced_in_a_source_file_6711": "Określ nazwy pakietów typów do uwzględnienia bez odwoływania się do nich w pliku źródłowym.", "Specify_what_JSX_code_is_generated_6646": "<PERSON><PERSON><PERSON><PERSON>, jaki kod JSX jest generowany.", "Specify_what_approach_the_watcher_should_use_if_the_system_runs_out_of_native_file_watchers_6634": "<PERSON><PERSON><PERSON><PERSON>, jaki<PERSON> pod<PERSON> ma s<PERSON> obserwator, je<PERSON><PERSON> w systemie zabraknie natywnych obserwatorów plików.", "Specify_what_module_code_is_generated_6657": "<PERSON><PERSON><PERSON><PERSON>, jaki kod modułów jest generowany.", "Split_all_invalid_type_only_imports_1367": "Podziel wszystkie nieprawidłowe importy dotyczące tylko typu", "Split_into_two_separate_import_declarations_1366": "Podziel na dwie osobne deklaracje importu", "Spread_operator_in_new_expressions_is_only_available_when_targeting_ECMAScript_5_and_higher_2472": "Operator roz<PERSON>ętości w wyrażeniach „new” jest dostępny tylko w<PERSON>y, gdy jest używany język ECMAScript 5 lub nowszy.", "Spread_types_may_only_be_created_from_object_types_2698": "Typy spread można tworzyć tylko z typów obiektu.", "Starting_compilation_in_watch_mode_6031": "Trwa uruchamianie kompilacji w trybie śledzenia...", "Statement_expected_1129": "Oczekiwan<PERSON> instrukcji.", "Statements_are_not_allowed_in_ambient_contexts_1036": "Instrukcje są niedozwolone w otaczających kontekstach.", "Static_members_cannot_reference_class_type_parameters_2302": "Statyczne składowe nie mogą przywoływać parametrów typu klasy.", "Static_property_0_conflicts_with_built_in_property_Function_0_of_constructor_function_1_2699": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> statyczna „{0}” jest w konflikcie z właściwością wbudowaną „Function.{0}” funkcji konstruktora „{1}”.", "String_literal_expected_1141": "Oczekiwano literału ciągu.", "String_literal_import_and_export_names_are_not_supported_when_the_module_flag_is_set_to_es2015_or_es_18057": "Nazwy importu i eksportu literału ciągu nie są obsługiwane, gdy flaga „--module” ma warto<PERSON> „es2015” lub „es2020”.", "String_literal_with_double_quotes_expected_1327": "Oczekiwano literału ciągu z podwójnymi cudzysłowami.", "Stylize_errors_and_messages_using_color_and_context_experimental_6073": "Stosuj styl dla błędów i komunikatów za pomocą koloru i kontekstu. (eksperymentalne).", "Subpattern_flags_must_be_present_when_there_is_a_minus_sign_1504": "Flagi wzorca podrzędnego muszą być obecne, gdy istnieje pół<PERSON>uza.", "Subsequent_property_declarations_must_have_the_same_type_Property_0_must_be_of_type_1_but_here_has_t_2717": "Kolejne deklaracje właściwości muszą być tego samego typu. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” musi być typu „{1}”, ale w tym miejscu jest typu „{2}”.", "Subsequent_variable_declarations_must_have_the_same_type_Variable_0_must_be_of_type_1_but_here_has_t_2403": "Kolejne deklaracje zmiennej muszą być tego samego typu. Zmienna „{0}” musi być typu „{1}”, ale w tym miej<PERSON>cu jest typu „{2}”.", "Substitution_0_for_pattern_1_has_incorrect_type_expected_string_got_2_5064": "Podstawienie „{0}” dla wzor<PERSON> „{1}” ma nieprawidłowy typ. Oczekiwano typu „string”, a uzyskano typ „{2}”.", "Substitution_0_in_pattern_1_can_have_at_most_one_Asterisk_character_5062": "Podstawienie „{0}” we wzorcu „{1}” może zawierać maksymalnie jeden znak „*”", "Substitutions_for_pattern_0_should_be_an_array_5063": "Podstawieniami wzorca „{0}” powinna być tablica.", "Substitutions_for_pattern_0_shouldn_t_be_an_empty_array_5066": "Podstawienia dla wzorca „{0}” nie powinny być pustą tablicą.", "Successfully_created_a_tsconfig_json_file_6071": "Pomyślnie utworzono plik tsconfig.json.", "Super_calls_are_not_permitted_outside_constructors_or_in_nested_functions_inside_constructors_2337": "Wywołania super są niedozwolone poza konstruktorami i zagnieżdżonymi funkcjami wewnątrz konstruktorów.", "Suppress_excess_property_checks_for_object_literals_6072": "Pomiń nadmiarowe sprawdzenia właściwości dla literałów obiektu.", "Suppress_noImplicitAny_errors_for_indexing_objects_lacking_index_signatures_6055": "Pomiń błędy noImplicitAny dotyczące obiektów indeksowania bez sygnatur indeksów.", "Suppress_noImplicitAny_errors_when_indexing_objects_that_lack_index_signatures_6703": "Pomija<PERSON> „noImplicitAny” podczas indeksowania obiektów, które nie mają sygnatur indeksu.", "Switch_each_misused_0_to_1_95138": "Zmień każdy niepoprawnie użyty element „{0}” na „{1}”", "Synchronously_call_callbacks_and_update_the_state_of_directory_watchers_on_platforms_that_don_t_supp_6704": "Synchronicznie wywołuj wywołania zwrotne i aktualizuj stan obserwatorów katalogów na platformach, które nie obsługują natywnie obserwacji rekursywnej.", "Syntax_Colon_0_6023": "Składnia: {0}", "Tag_0_expects_at_least_1_arguments_but_the_JSX_factory_2_provides_at_most_3_6229": "Tag „{0}” oczekuje co najmniej „{1}” argumentów, ale fabryka JSX „{2}” dostarcza maksymalnie „{3}”.", "Tagged_template_expressions_are_not_permitted_in_an_optional_chain_1358": "Oznakowane wyrażenia szablonu nie są dozwolone w opcjonalnym łańcuchu.", "Target_allows_only_0_element_s_but_source_may_have_more_2621": "Liczba elementów dozwolonych przez element docelowy to {0}, ale źródło może mieć ich więcej.", "Target_requires_0_element_s_but_source_may_have_fewer_2620": "Liczba elementów wymaganych przez element docelowy to {0}, ale źródło może mieć ich mniej.", "Target_signature_provides_too_few_arguments_Expected_0_or_more_but_got_1_2849": "Podpis docelowy zawiera zbyt mało argumentów. Oczekiwano {0} lub wi<PERSON><PERSON><PERSON>, ale otrzymano {1}.", "The_0_modifier_can_only_be_used_in_TypeScript_files_8009": "Modyfikatora „{0}” można używać tylko w plikach TypeScript.", "The_0_operator_cannot_be_applied_to_type_symbol_2469": "Nie można zastosować operatora „{0}” do typu „symbol”.", "The_0_operator_is_not_allowed_for_boolean_types_Consider_using_1_instead_2447": "Operator „{0}” nie jest dozwolony w przypadku typów logicznych. Zamiast tego rozważ użycie operatora „{1}”.", "The_0_property_of_an_async_iterator_must_be_a_method_2768": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” iteratora asynchronicznego musi by<PERSON> metod<PERSON>.", "The_0_property_of_an_iterator_must_be_a_method_2767": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” iteratora musi być metodą.", "The_Object_type_is_assignable_to_very_few_other_types_Did_you_mean_to_use_the_any_type_instead_2696": "Typ „Object” można przypisać do niewielu innych typów. <PERSON>zy zamiast tego typu miał zostać użyty typ „any”?", "The_Unicode_u_flag_and_the_Unicode_Sets_v_flag_cannot_be_set_simultaneously_1502": "Nie można jednocześnie ustawić flagi Unicode (u) i flagi Unicode Sets (v).", "The_arguments_object_cannot_be_referenced_in_an_arrow_function_in_ES5_Consider_using_a_standard_func_2496": "Obiekt „arguments” nie może być przywoływany w funkcji strzałkowej w języku ES5. Rozważ użycie standardowego wyrażenia funkcji.", "The_arguments_object_cannot_be_referenced_in_an_async_function_or_method_in_ES5_Consider_using_a_sta_2522": "Obiekt „arguments” nie może być przywoływany w asynchronicznej funkcji lub metodzie w języku ES5. Rozważ użycie standardowej funkcji lub metody.", "The_body_of_an_if_statement_cannot_be_the_empty_statement_1313": "<PERSON><PERSON><PERSON><PERSON> instrukcji „if” nie może być pustą instrukcją.", "The_call_would_have_succeeded_against_this_implementation_but_implementation_signatures_of_overloads_2793": "Wywołanie powiodłoby się dla tej implementacji, ale sygnatury implementacji przeciążeń nie są widoczne na zewnątrz.", "The_character_set_of_the_input_files_6163": "Zestaw znaków plików wejściowych.", "The_containing_arrow_function_captures_the_global_value_of_this_7041": "Zawierająca funkcja strzałki przechwytuje wartość globalną parametru „this”.", "The_containing_function_or_module_body_is_too_large_for_control_flow_analysis_2563": "T<PERSON>ść zawierającej funkcji lub modułu jest za duża do analizy przepływu sterowania.", "The_current_file_is_a_CommonJS_module_and_cannot_use_await_at_the_top_level_1309": "Bieżący plik jest modułem CommonJS i nie może używać elementu „await” na najwyższym poziomie.", "The_current_file_is_a_CommonJS_module_whose_imports_will_produce_require_calls_however_the_reference_1479": "Bieżący plik jest modułem CommonJS, którego importy będą generować wywołania „require”; jednak przywoływany plik jest modułem ECMAScript i nie można go zaimportować za pomocą wywołania „require”. Zamiast tego rozważ zapisanie dynamicznego wywołania „import(\"{0}\")”.", "The_current_host_does_not_support_the_0_option_5001": "Bieżący host nie obsługuje opcji „{0}”.", "The_declaration_of_0_that_you_probably_intended_to_use_is_defined_here_18018": "W tym miej<PERSON>cu zdefiniowano deklarację elementu „{0}”, której prawdopodobnie zamierzano użyć", "The_declaration_was_marked_as_deprecated_here_2798": "Deklaracja została oznaczona jako przestarzała w tym <PERSON>.", "The_expected_type_comes_from_property_0_which_is_declared_here_on_type_1_6500": "Oczekiwany typ pochodzi z właściwości „{0}”, która jest zadeklarowana tutaj w typie „{1}”", "The_expected_type_comes_from_the_return_type_of_this_signature_6502": "Oczekiwany typ pochodzi ze zwracanego typu tej sygnatury.", "The_expected_type_comes_from_this_index_signature_6501": "Oczekiwany typ pochodzi z tej sygnatury indeksu.", "The_expression_of_an_export_assignment_must_be_an_identifier_or_qualified_name_in_an_ambient_context_2714": "Wyrażenie przypisania eksportu musi być identyfikatorem lub kwalifikowaną nazwą w otaczającym kontekście.", "The_file_is_in_the_program_because_Colon_1430": "Plik jest w programie, ponieważ:", "The_files_list_in_config_file_0_is_empty_18002": "Lista „files” w pliku konfiguracji „{0}” jest pusta.", "The_first_export_default_is_here_2752": "Pierwszy element export default jest tutaj.", "The_first_parameter_of_the_then_method_of_a_promise_must_be_a_callback_1060": "Pierwszym parametrem metody „then” obietnicy musi być wywołanie zwrotne.", "The_global_type_JSX_0_may_not_have_more_than_one_property_2608": "Dla typu globalnego „JSX.{0}” nie można okre<PERSON> więcej niż jednej wła<PERSON>ści.", "The_implementation_signature_is_declared_here_2750": "Sygnatura implementacji jest zadeklarowana tutaj.", "The_import_meta_meta_property_is_not_allowed_in_files_which_will_build_into_CommonJS_output_1470": "Met<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „import.meta” jest niedozwolona w plikach, które będą kompilowane w danych wyjściowych CommonJS.", "The_import_meta_meta_property_is_only_allowed_when_the_module_option_is_es2020_es2022_esnext_system__1343": "Meta-w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „import.meta” jest dozwolona tylko wtedy, gdy opcja „--module” ma warto<PERSON> „es2020”, „es2022”, „esnext”, „system”, „node16” lub „nodenext”.", "The_inferred_type_of_0_cannot_be_named_without_a_reference_to_1_This_is_likely_not_portable_A_type_a_2742": "Wywnioskowany typ „{0}” nie może być nazwany bez odwołania do elementu „{1}”. Prawdopodobnie nie jest to przenośne. Konieczna jest adnotacja typu.", "The_inferred_type_of_0_references_a_type_with_a_cyclic_structure_which_cannot_be_trivially_serialize_5088": "Wywnioskowany typ elementu „{0}” odwołuje się do typu ze strukturą cykliczną, którego nie można serializować w prosty sposób. Wymagana jest adnotacja typu.", "The_inferred_type_of_0_references_an_inaccessible_1_type_A_type_annotation_is_necessary_2527": "Wnioskowany typ „{0}” przywołuje niedostępny typ „{1}”. Adnotacja typu jest konieczna.", "The_inferred_type_of_this_node_exceeds_the_maximum_length_the_compiler_will_serialize_An_explicit_ty_7056": "Wywnioskowany typ tego węzła przekracza maksymalną d<PERSON>, którą kompilator może serializować. Wymagana jest jawna adnotacja typu.", "The_initializer_of_a_using_declaration_must_be_either_an_object_with_a_Symbol_dispose_method_or_be_n_2850": "<PERSON><PERSON><PERSON><PERSON> „using” musi być obiektem z metodą „[Symbol.dispose]()” albo mie<PERSON> war<PERSON> „null” lub „undefined”.", "The_initializer_of_an_await_using_declaration_must_be_either_an_object_with_a_Symbol_asyncDispose_or_2851": "<PERSON><PERSON><PERSON><PERSON> „await using” musi być obiektem z metodą „[Symbol.asyncDispose]()” lub „[Symbol.dispose]5D;()” albo mie<PERSON> war<PERSON> „null” lub „undefined”.", "The_intersection_0_was_reduced_to_never_because_property_1_exists_in_multiple_constituents_and_is_pr_18032": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” zostało zredukowane do wartości „never”, poni<PERSON><PERSON><PERSON> w<PERSON> „{1}” istnieje w wielu elementach składowych i w części z nich jest prywatna.", "The_intersection_0_was_reduced_to_never_because_property_1_has_conflicting_types_in_some_constituent_18031": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” zostało zredukowane do wartości „never”, poni<PERSON><PERSON><PERSON> w<PERSON> „{1}” zawiera typy powodujące konflikt w niektórych elementach składowych.", "The_intrinsic_keyword_can_only_be_used_to_declare_compiler_provided_intrinsic_types_2795": "Słowa kluczowego „intrinsic” można używać tylko do deklarowania typów wewnętrznych udostępnianych przez kompilator.", "The_jsxFragmentFactory_compiler_option_must_be_provided_to_use_JSX_fragments_with_the_jsxFactory_com_17016": "Należy podać opcję kompilatora „jsxFragmentFactory”, aby używać fragmentów JSX z opcją kompilatora „jsxFactory”.", "The_last_overload_gave_the_following_error_2770": "Ostatnie przeciążenie dało następujący błąd.", "The_last_overload_is_declared_here_2771": "Ostatnie przeciążenie jest zadeklarowane tutaj.", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_destructuring_pattern_2491": "Lewa strona instrukcji „for...in” nie może być wzorcem usuwającym strukturę.", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_using_declaration_1493": "Lewa strona instrukcji „for...in” nie może by<PERSON> „using”.", "The_left_hand_side_of_a_for_in_statement_cannot_be_an_await_using_declaration_1494": "Le<PERSON> strona instrukcji „for...in” nie może by<PERSON> „await using”.", "The_left_hand_side_of_a_for_in_statement_cannot_use_a_type_annotation_2404": "Lewa strona instrukcji „for...in” nie może używać adnotacji typu.", "The_left_hand_side_of_a_for_in_statement_may_not_be_an_optional_property_access_2780": "Lewa strona instrukcji „for...in” nie może być opcjonalnym dostępem do właściwości.", "The_left_hand_side_of_a_for_in_statement_must_be_a_variable_or_a_property_access_2406": "Lewa strona instrukcji „for...in” musi być zmienną lub dostępem do właściwości.", "The_left_hand_side_of_a_for_in_statement_must_be_of_type_string_or_any_2405": "<PERSON><PERSON> strona instrukcji „for...in” musi by<PERSON> typu „string” lub „any”.", "The_left_hand_side_of_a_for_of_statement_cannot_use_a_type_annotation_2483": "Lewa strona instrukcji „for...of” nie może używać adnotacji typu.", "The_left_hand_side_of_a_for_of_statement_may_not_be_an_optional_property_access_2781": "Lewa strona instrukcji „for...of” nie może być opcjonalnym dostępem do właściwości.", "The_left_hand_side_of_a_for_of_statement_may_not_be_async_1106": "Lewa strona instrukcji „for...of” nie może być „asynchroniczna”.", "The_left_hand_side_of_a_for_of_statement_must_be_a_variable_or_a_property_access_2487": "Lewa strona instrukcji „for...of” musi być zmienną lub dostępem do właściwości.", "The_left_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2362": "Lewa strona operacji arytmetycznej musi być typu „any”, „number”, „bigint” lub typu wyliczeniowego.", "The_left_hand_side_of_an_assignment_expression_may_not_be_an_optional_property_access_2779": "Lewa strona wyrażenia przypisania nie może być opcjonalnym dostępem do właściwości.", "The_left_hand_side_of_an_assignment_expression_must_be_a_variable_or_a_property_access_2364": "Lewa strona wyrażenia przypisania musi być zmienną lub dostępem do właściwości.", "The_left_hand_side_of_an_instanceof_expression_must_be_assignable_to_the_first_argument_of_the_right_2860": "Lewa strona wyrażenia „instanceof” musi być przypisana do pierwszego argumentu po prawej stronie metody „[Symbol.hasInstance]”.", "The_left_hand_side_of_an_instanceof_expression_must_be_of_type_any_an_object_type_or_a_type_paramete_2358": "Lewa strona wyrażenia „instanceof” musi być typu „any”, typu obiektu lub parametrem typu.", "The_locale_used_when_displaying_messages_to_the_user_e_g_en_us_6156": "Ustawienia regionalne używane przy wyświetlaniu komunikatów użytkownikowi (np. „pl-pl”)", "The_maximum_dependency_depth_to_search_under_node_modules_and_load_JavaScript_files_6136": "Maksymalna głębokość zależności na potrzeby wyszukiwania w elemencie node_modules i ładownia plików JavaScript.", "The_operand_of_a_delete_operator_cannot_be_a_private_identifier_18011": "Operand operatora „delete” nie może być identyfikatorem prywatnym.", "The_operand_of_a_delete_operator_cannot_be_a_read_only_property_2704": "Operand operatora „delete” nie może być właściwością tylko do odczytu.", "The_operand_of_a_delete_operator_must_be_a_property_reference_2703": "Operand operatora „delete” musi być odwołaniem do właściwości.", "The_operand_of_a_delete_operator_must_be_optional_2790": "Operand operatora „delete” musi być opcjonalny.", "The_operand_of_an_increment_or_decrement_operator_may_not_be_an_optional_property_access_2777": "Operand operatora inkrementacji lub dekrementacji nie może być opcjonalnym dostępem do właściwości.", "The_operand_of_an_increment_or_decrement_operator_must_be_a_variable_or_a_property_access_2357": "Operand operatora inkrementacji lub dekrementacji musi być zmienną lub dostępem do właściwości.", "The_parser_expected_to_find_a_1_to_match_the_0_token_here_1007": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>ł znalezienia elementu „{1}” w celu dopasowania do tokenu „{0}” w tym mi<PERSON>.", "The_project_root_is_ambiguous_but_is_required_to_resolve_export_map_entry_0_in_file_1_Supply_the_roo_2209": "Katalog główny projektu jest niejednoznaczny, a jest wymagany do rozpoznania wpisu mapy eksportu „{0}” w pliku „{1}”. Podaj opcję kompilatora „rootDir”, aby usun<PERSON> niejednoznaczności.", "The_project_root_is_ambiguous_but_is_required_to_resolve_import_map_entry_0_in_file_1_Supply_the_roo_2210": "Katalog główny projektu jest niejednoznaczny, a jest wymagany do rozpoznania wpisu mapy importu „{0}” w pliku „{1}”. Podaj opcję kompilatora „rootDir”, aby usun<PERSON> niejednoznaczności.", "The_property_0_cannot_be_accessed_on_type_1_within_this_class_because_it_is_shadowed_by_another_priv_18014": "Nie można uzyskać dostępu do właściwości „{0}” w typie „{1}” w tej klasie, ponieważ jest ona zasłaniana przez inny identyfikator prywatny o takiej samej pisowni.", "The_return_type_of_a_parameter_decorator_function_must_be_either_void_or_any_1237": "Zwracany typ funkcji dekoratora parametrów musi mieć postać „void” lub „any”.", "The_return_type_of_a_property_decorator_function_must_be_either_void_or_any_1236": "Zwracany typ funkcji dekoratora właściwości musi mieć postać „void” lub „any”.", "The_return_type_of_an_async_function_must_either_be_a_valid_promise_or_must_not_contain_a_callable_t_1058": "Zwracany typ funkcji asynchronicznej musi być praw<PERSON> obietnicą lub nie może zaw<PERSON> wywoływalnej składowej „then”.", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_1065": "Zwracany typ metody lub <PERSON>ji asynchronicznej musi być globalnym typem Promise<T>.", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_Did_you_mean_to_wri_1064": "Zwracany typ funkcji lub metody asynchronicznej musi być globalnym typem Promise<T>. <PERSON><PERSON> chodził<PERSON>i o typ „Promise<{0}>”?", "The_right_hand_side_of_a_for_in_statement_must_be_of_type_any_an_object_type_or_a_type_parameter_but_2407": "<PERSON><PERSON><PERSON> strona instruk<PERSON>ji „for...in” musi zawierać typ „any”, typ obiektu lub parametr typu, a tutaj ma typ „{0}”.", "The_right_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2363": "Prawa strona operacji arytmetycznej musi być typu „any”, „number”, „bigint” lub typu wyliczeniowego.", "The_right_hand_side_of_an_instanceof_expression_must_be_either_of_type_any_a_class_function_or_other_2359": "Prawa strona wyrażenia „instanceof” musi być typem „any”, klasą, funkcją lub innym typem, który można przypisać do typu interfejsu „Function”, albo typem obiektu z metodą „Symbol.hasInstance”.", "The_right_hand_side_of_an_instanceof_expression_must_not_be_an_instantiation_expression_2848": "Prawa strona wyrażenia „instanceof” nie może być wyrażeniem tworzenia wystąpienia.", "The_root_value_of_a_0_file_must_be_an_object_5092": "<PERSON><PERSON><PERSON>ć katalogu głównego pliku „{0}” musi być obiektem.", "The_runtime_will_invoke_the_decorator_with_1_arguments_but_the_decorator_expects_0_1278": "Środowisko uruchomieniowe wywoła dekoratora z argumentami {1}, ale dekorator oczekuje {0}.", "The_runtime_will_invoke_the_decorator_with_1_arguments_but_the_decorator_expects_at_least_0_1279": "Środowisko uruchomieniowe wywoła dekoratora z argumentami {1}, ale dekorator oczekuje co najmniej {0}.", "The_shadowing_declaration_of_0_is_defined_here_18017": "Deklaracja przesłaniania „{0}” jest zdefiniowana tutaj", "The_signature_0_of_1_is_deprecated_6387": "Sygna<PERSON> „{0}” elementu „{1}” jest przestarzała.", "The_specified_path_does_not_exist_Colon_0_5058": "Wybrana ścieżka nie istnieje: „{0}”.", "The_tag_was_first_specified_here_8034": "Tag został najpierw określony w tym miejscu.", "The_target_of_an_object_rest_assignment_may_not_be_an_optional_property_access_2778": "Obiekt docelowy przypisania rest obiektu nie może być opcjonalnym dostępem do właściwości.", "The_target_of_an_object_rest_assignment_must_be_a_variable_or_a_property_access_2701": "Cel przypisania rest obiektu musi stanowić dostęp do zmiennej lub właściwości.", "The_this_context_of_type_0_is_not_assignable_to_method_s_this_of_type_1_2684": "<PERSON><PERSON><PERSON><PERSON> „this” typu „{0}” nie można przypisać do elementu „this” metody typu „{1}”.", "The_this_types_of_each_signature_are_incompatible_2685": "<PERSON><PERSON> „this” sygnatur nie są zgodne.", "The_type_0_is_readonly_and_cannot_be_assigned_to_the_mutable_type_1_4104": "<PERSON>p „{0}” jest „readonly” i nie można go przypisać do typu modyfikowalnego „{1}”.", "The_type_modifier_cannot_be_used_on_a_named_export_when_export_type_is_used_on_its_export_statement_2207": "Modyfikatora „type” nie można użyć w nazwanym eksporcie, gdy w instrukcji eksportowania jest używany element „export type”.", "The_type_modifier_cannot_be_used_on_a_named_import_when_import_type_is_used_on_its_import_statement_2206": "Modyfikatora „type” nie można użyć w nazwanym imporcie, gdy w instrukcji importowania jest używany element „import type”.", "The_type_of_a_function_declaration_must_match_the_function_s_signature_8030": "Typ deklaracji funkcji musi być zgodny z sygnaturą funkcji.", "The_type_of_this_node_cannot_be_serialized_because_its_property_0_cannot_be_serialized_4118": "Typ tego węzła nie może być serializowany, poniew<PERSON>ż jego wła<PERSON><PERSON><PERSON><PERSON> „{0}” nie może być serializowana.", "The_type_returned_by_the_0_method_of_an_async_iterator_must_be_a_promise_for_a_type_with_a_value_pro_2547": "Typ zwracany przez metodę „{0}()” iteratora asynchronicznego musi być obietnicą dla typu z właściwością „value”.", "The_type_returned_by_the_0_method_of_an_iterator_must_have_a_value_property_2490": "Typ zwracany przez metodę „{0}()” iteratora musi mieć właściwość „value”.", "The_types_of_0_are_incompatible_between_these_types_2200": "Typy elementu „{0}” są niezgodne między tymi typami.", "The_types_returned_by_0_are_incompatible_between_these_types_2201": "Typy zwrócone przez element „{0}” są niezgodne między tymi typami.", "The_value_0_cannot_be_used_here_18050": "W tym miej<PERSON>cu nie można użyć wartości „{0}”.", "The_variable_declaration_of_a_for_in_statement_cannot_have_an_initializer_1189": "Deklaracja zmiennej instrukcji „for...in” nie może mieć inicjatora.", "The_variable_declaration_of_a_for_of_statement_cannot_have_an_initializer_1190": "Deklaracja zmiennej instrukcji „for...of” nie może mieć inicjatora.", "The_with_statement_is_not_supported_All_symbols_in_a_with_block_will_have_type_any_2410": "Instruk<PERSON><PERSON> „with” nie jest obsługiwana. Wszystkie symbole w bloku „with” będą mieć typ „any”.", "There_are_types_at_0_but_this_result_could_not_be_resolved_under_your_current_moduleResolution_setti_6280": "Istnieją typy w „{0}”, ale nie można rozpoznać tego wyniku w bieżącym ustawieniu „moduleResolution”. Rozważ zaktualizowanie do wartości „node16”, „nodenext” lub „bundler”.", "There_are_types_at_0_but_this_result_could_not_be_resolved_when_respecting_package_json_exports_The__6278": "Istnieją typy w „{0}”, ale nie można rozpoznać tego wyniku podczas uwzględniania pliku package.json „exports”. Biblioteka „{1}” może wymagać zaktualizowania pliku package.json lub wpisywania tekstu.", "There_is_no_capturing_group_named_0_in_this_regular_expression_1532": "W tym wyrażeniu regularnym nie ma żadnej grupy przechwytywania o nazwie „{0}”.", "There_is_nothing_available_for_repetition_1507": "Brak dostępnych elementów do powtórzenia.", "This_JSX_tag_s_0_prop_expects_a_single_child_of_type_1_but_multiple_children_were_provided_2746": "Element prop „{0}” tego tagu JSX oczekuje pojedynczego elementu podrzędnego typu „{1}”, ale podano wiele elementów podrzędnych.", "This_JSX_tag_s_0_prop_expects_type_1_which_requires_multiple_children_but_only_a_single_child_was_pr_2745": "Element prop „{0}” tego tagu JSX oczekuje typu „{1}”, który wymaga wielu elementów podrzędnych, ale podano tylko jeden element podrzędny.", "This_backreference_refers_to_a_group_that_does_not_exist_There_are_no_capturing_groups_in_this_regul_1534": "To odwołanie wsteczne odwołuje się do grupy, która nie istnieje. W tym wyrażeniu regularnym nie ma żadnych grup przechwytywania.", "This_backreference_refers_to_a_group_that_does_not_exist_There_are_only_0_capturing_groups_in_this_r_1533": "To odwołanie wsteczne odwołuje się do grupy, która nie istnieje. W tym wyrażeniu regularnym jest tylko następująca liczba grup przechwytywania: {0}.", "This_binary_expression_is_never_nullish_Are_you_missing_parentheses_2870": "To wyrażenie binarne nigdy nie dopuszcza wartości null. <PERSON>zy brakuje nawiasów?", "This_character_cannot_be_escaped_in_a_regular_expression_1535": "Tego znaku nie można poprzedzić znakiem ucieczki w wyrażeniu regularnym.", "This_comparison_appears_to_be_unintentional_because_the_types_0_and_1_have_no_overlap_2367": "To porównanie wydaje się niezamierzone, poni<PERSON><PERSON><PERSON> typy „{0}” i „{1}” nie nakładają się na siebie.", "This_condition_will_always_return_0_2845": "Ten warunek będzie zawsze zwracać wartość „{0}”.", "This_condition_will_always_return_0_since_JavaScript_compares_objects_by_reference_not_value_2839": "Ten warunek zawsze będzie zwrac<PERSON> wartość „{0}”, ponieważ język JavaScript porównuje obiekty według odwołania, a nie wartości.", "This_condition_will_always_return_true_since_this_0_is_always_defined_2801": "Ten warunek będzie zawsze zwracać wartość true, poni<PERSON><PERSON><PERSON> wartoś<PERSON> '{0}' jest zaws<PERSON> prawdziwa.", "This_condition_will_always_return_true_since_this_function_is_always_defined_Did_you_mean_to_call_it_2774": "Ten warunek będzie zawsze zwracał wartość true, poniew<PERSON>ż funkcja jest zawsze zdefiniowana. <PERSON><PERSON> ch<PERSON>z wywołać ją zamiast tego?", "This_constructor_function_may_be_converted_to_a_class_declaration_80002": "Ta funkcja konstruktora może zostać przekonwertowana na deklarację klasy.", "This_expression_is_always_nullish_2871": "To wyrażenie ma zawsze warto<PERSON> null.", "This_expression_is_not_callable_2349": "To wyrażenie nie jest wywoły<PERSON>ne.", "This_expression_is_not_callable_because_it_is_a_get_accessor_Did_you_mean_to_use_it_without_6234": "Tego wyrażenia nie można wywoływać, poni<PERSON><PERSON><PERSON> jest to metoda dostępu „get”. <PERSON><PERSON> chodziło Ci o użycie go bez znaków „()”?", "This_expression_is_not_constructable_2351": "Tego wyrażenia nie można skonstruować.", "This_file_already_has_a_default_export_95130": "Ten plik ma już domyślny eksport", "This_is_the_declaration_being_augmented_Consider_moving_the_augmenting_declaration_into_the_same_fil_6233": "To jest roz<PERSON><PERSON><PERSON><PERSON> de<PERSON>. Rozważ przeniesienie deklaracji rozszerzenia do tego samego pliku.", "This_kind_of_expression_is_always_falsy_2873": "Tego rodzaju wyrażenie jest zawsze błędne.", "This_kind_of_expression_is_always_truthy_2872": "Tego rodzaju wyrażenie jest zawsze prawdziwe.", "This_may_be_converted_to_an_async_function_80006": "To można przekonwertować na funkcję asynchroniczną.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4122": "Ta składowa nie może mieć komentarza JSDoc z tagiem „@override”, ponieważ nie jest zadeklarowany w klasie bazowej „{0}”.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4123": "Ta składowa nie może mieć komentarza JSDoc z tagiem „override”, ponieważ nie jest zadeklarowany w klasie bazowej „{0}”. <PERSON><PERSON> chodziło Ci o „{1}”?", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_containing_class_0_does_not_4121": "Ta składowa nie może mieć komentarza JSDoc z tagiem „@override”, poniew<PERSON>ż jego klasa zawierająca „{0}” nie rozszerza innej klasy.", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_4113": "Ten element cz<PERSON><PERSON>kowski nie może mieć modyfikatora „override”, ponieważ nie jest zadeklarowany w klasie podstawowej „{0}”.", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_Did_you__4117": "Ten element c<PERSON><PERSON><PERSON><PERSON> nie może mieć modyfikatora \"override\", p<PERSON><PERSON><PERSON><PERSON> nie jest on zadeklarowany w klasie bazowej \"{0}\". <PERSON><PERSON> chodzi <PERSON> o \"{1}\"?", "This_member_cannot_have_an_override_modifier_because_its_containing_class_0_does_not_extend_another__4112": "Ten element członkowski nie może mieć modyfikatora „override”, ponieważ jego klasa zawierająca „{0}” nie rozszerza innej klasy.", "This_member_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_in_the_base_4119": "Ta składowa musi mieć komentarz JSDoc z tagiem „@override”, ponieważ zastępuje składową w klasie bazowej „{0}”.", "This_member_must_have_an_override_modifier_because_it_overrides_a_member_in_the_base_class_0_4114": "Ten element członkowski musi mieć modyfikator „override”, ponieważ przesłania on element członkowski w klasie podstawowej „{0}”.", "This_member_must_have_an_override_modifier_because_it_overrides_an_abstract_method_that_is_declared__4116": "Ten element czł<PERSON>kowski musi mieć modyfikator „override”, ponieważ zastępuje metodę abstrakcyjną zadeklarowaną w klasie podstawowej „{0}”.", "This_module_can_only_be_referenced_with_ECMAScript_imports_Slashexports_by_turning_on_the_0_flag_and_2497": "Do tego modułu można odwoływać się tylko za pomocą importów/eksportów języka ECMAScript, włączając flagę „{0}” i odwołując się do jego eksportu domyślnego.", "This_module_is_declared_with_export_and_can_only_be_used_with_a_default_import_when_using_the_0_flag_2594": "Ten moduł jest zadeklarowany przy użyciu składni „export =” i może być używany tylko z importem domyślnym, gdy jest używana flaga „{0}”.", "This_operation_can_be_simplified_This_shift_is_identical_to_0_1_2_6807": "Tę operację można <PERSON>. Ta zmiana jest identyczna z „{0} {1} {2}”.", "This_overload_implicitly_returns_the_type_0_because_it_lacks_a_return_type_annotation_7012": "To przeciążenie niejawnie zwraca typ „{0}”, ponieważ nie ma adnotacji zwracanego typu.", "This_overload_signature_is_not_compatible_with_its_implementation_signature_2394": "Ta sygnatura przeciążenia nie jest zgodna z jej sygnaturą implementacji.", "This_parameter_is_not_allowed_with_use_strict_directive_1346": "Ten parametr nie jest dozwolony w dyrektywie „use strict”.", "This_parameter_property_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_4120": "Ta właściwość parametru musi mieć komentarz JSDoc z tagiem „@override”, ponieważ zastępuje składową w klasie bazowej „{0}”.", "This_parameter_property_must_have_an_override_modifier_because_it_overrides_a_member_in_base_class_0_4115": "<PERSON> właś<PERSON><PERSON><PERSON>ć parametru musi mieć modyfikator \"override\", poni<PERSON><PERSON><PERSON> zastępuje on członka w klasie bazowej \"{0}\".", "This_regular_expression_flag_cannot_be_toggled_within_a_subpattern_1509": "Tej flagi wyrażenia regularnego nie można przełączać w obrębie wzorca podrzędnego.", "This_regular_expression_flag_is_only_available_when_targeting_0_or_later_1501": "Ta flaga wyrażenia regularnego jest dostępna tylko w przypadku określania wartości docelowej „{0}” lub now<PERSON>.", "This_spread_always_overwrites_this_property_2785": "To rozmieszczenie zawsze powoduje zastąpienie tej właściwości.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Add_a_trailing_comma_or_explicit_cons_7060": "Ta składnia jest zarezerwowana w plikach z rozszerzeniem .MTS lub CTS. Dodaj końcowy przecinek lub jawne ograniczenie.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Use_an_as_expression_instead_7059": "Ta składnia jest zarezerwowana w plikach z rozszerzeniem. MTS lub. CTS. Użyj zamiast tego wyrażenia „as”.", "This_syntax_requires_an_imported_helper_but_module_0_cannot_be_found_2354": "Ta składnia wymaga zaimportowanego pomocnika, ale nie można znaleźć modułu „{0}”.", "This_syntax_requires_an_imported_helper_named_1_which_does_not_exist_in_0_Consider_upgrading_your_ve_2343": "Ta składnia wymaga zaimportowanego pomocnika o nazwie „{1}”, który nie istnieje w elemencie „{0}”. Rozważ uaktualnienie wersji „{0}”.", "This_syntax_requires_an_imported_helper_named_1_with_2_parameters_which_is_not_compatible_with_the_o_2807": "Ta składnia wymaga zaimportowanego pomocnika o nazwie „{1}” z parametrami {2}, kt<PERSON>ry nie jest zgodny z tym w elemencie „{0}”. Rozważ uaktualnienie wersji elementu „{0}”.", "This_type_parameter_might_need_an_extends_0_constraint_2208": "Ten parametr typu może wymagać ograni<PERSON> „rozszerzeń{0}”.", "This_use_of_import_is_invalid_import_calls_can_be_written_but_they_must_have_parentheses_and_cannot__1326": "To użycie elementu „import” jest ni<PERSON>rawidł<PERSON>e. Wywołania „import()” mogą by<PERSON> zap<PERSON>wan<PERSON>, ale muszą mieć nawiasy i nie mogą mieć argumentów typu.", "To_convert_this_file_to_an_ECMAScript_module_add_the_field_type_Colon_module_to_0_1482": "Aby przekonwertować ten plik na moduł ECMAScript, dodaj pole „\"type\": \"module\"” do „{0}”.", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_add_the_field_type_Co_1481": "Aby przekonwertować ten plik na moduł ECMAScript, zmień rozszerzenie jego pliku na „{0}” lub dodaj pole „\"type\": \"module\"” do „{1}”.", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_create_a_local_packag_1480": "Aby przekonwertować ten plik na moduł ECMAScript, zmień rozszerzenie jego pliku na „{0}” lub utwórz lokalny plik package.json z polem „{ \"type\": \"module\" }\".", "To_convert_this_file_to_an_ECMAScript_module_create_a_local_package_json_file_with_type_Colon_module_1483": "Aby przekonwertować ten plik na moduł ECMAScript, utwórz lokalny plik package.json z polem „{ \"type\": \"module\" }\".", "Top_level_await_expressions_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_n_1378": "Wyrażenia „await” najwyższego poziomu są dozwolone tylko wtedy, gdy opcja „module” jest ustawiona na wartość „es2022”, „esnext”, „system”, „node16”, „nodenext” lub „preserve”, a opcja „target” jest ustawiona na wartość „es2017” lub wyższą.", "Top_level_await_using_statements_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_sys_2854": "Instrukcje „await using” najwyższego poziomu są dozwolone tylko wtedy, gdy opcja „module” jest ustawiona na wartość „es2022”, „esnext”, „system”, „node16”, „nodenext” lub „preserve”, a opcja „target” jest ustawiona na wartość „es2017” lub wyższą.", "Top_level_declarations_in_d_ts_files_must_start_with_either_a_declare_or_export_modifier_1046": "Deklaracje najwyższego poziomu w plikach .d.ts muszą rozpoczynać się od modyfikatora „declare” lub „export”.", "Top_level_for_await_loops_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_nod_1432": "Pętle „for await” najwyższego poziomu są dozwolone tylko wtedy, gdy opcja „module” jest ustawiona na wartość „es2022”, „esnext”, „system”, „node16”, „nodenext” lub „preserve”, a opcja „target” jest ustawiona na wartość „es2017” lub wyższą.", "Trailing_comma_not_allowed_1009": "Końcowy przecinek jest niedozwolony.", "Transpile_each_file_as_a_separate_module_similar_to_ts_transpileModule_6153": "Transpiluj każdy plik jako oddzielny moduł (podobne do „ts.transpileModule”).", "Try_npm_i_save_dev_types_Slash_1_if_it_exists_or_add_a_new_declaration_d_ts_file_containing_declare__7035": "Spróbuj użyć polecenia „npm i --save-dev @types/{1}”, je<PERSON><PERSON> is<PERSON>, lub dodać nowy plik deklaracji (.d.ts) zawierający ciąg „declare module '{0}';”", "Trying_other_entries_in_rootDirs_6110": "Wykonywanie prób przy użyciu innych pozycji opcji „rootDirs”.", "Trying_substitution_0_candidate_module_location_Colon_1_6093": "Wykonywanie próby przeprowadzenia podstawienia „{0}”, lokalizacja modułu kandydata: „{1}”.", "Tuple_type_0_of_length_1_has_no_element_at_index_2_2493": "<PERSON><PERSON> „{0}” o dług<PERSON> „{1}” nie ma żadnego elementu w indeksie „{2}”.", "Tuple_type_arguments_circularly_reference_themselves_4110": "Argumenty typu krotki cyklicznie odwołują się do samych siebie.", "Type_0_can_only_be_iterated_through_when_using_the_downlevelIteration_flag_or_with_a_target_of_es201_2802": "Po typie „{0}” można iterować tylko w<PERSON>, gdy jest używana flaga „--downlevelIteration” lub parametr „--target” ma warto<PERSON> „es2015” lub wyższą.", "Type_0_cannot_be_used_as_an_index_type_2538": "<PERSON><PERSON> „{0}” nie można używać jako typu indeksu.", "Type_0_cannot_be_used_to_index_type_1_2536": "<PERSON><PERSON> „{0}” nie można użyć do indeksowania typu „{1}”.", "Type_0_does_not_satisfy_the_constraint_1_2344": "Typ „{0}” nie spełnia warunków ograniczenia „{1}”.", "Type_0_does_not_satisfy_the_expected_type_1_1360": "<PERSON>p „{0}” nie spełnia oczekiwanego typu „{1}”.", "Type_0_has_no_call_signatures_2757": "Typ „{0}” nie ma sygnatur wywołania.", "Type_0_has_no_construct_signatures_2761": "Typ „{0}” nie ma sygnatur konstruk<PERSON>ji.", "Type_0_has_no_matching_index_signature_for_type_1_2537": "<PERSON>p „{0}” nie ma pasującej sygnatury indeksu dla typu „{1}”.", "Type_0_has_no_properties_in_common_with_type_1_2559": "<PERSON><PERSON> „{0}” i „{1}” nie mają żadnych wspólnych właściwości.", "Type_0_has_no_signatures_for_which_the_type_argument_list_is_applicable_2635": "Typ „{0}” nie ma pod<PERSON>, dla kt<PERSON><PERSON>ch ma zastosowanie lista argumentów typu ogólnego.", "Type_0_is_generic_and_can_only_be_indexed_for_reading_2862": "Typ „{0}” jest typem ogólnym i może być indeksowany tylko do odczytu.", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_2739": "W typie „{0}” brakuje następujących właściwości z typu „{1}”: {2}", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_and_3_more_2740": "W typie „{0}” brakuje następujących właściwości z typu „{1}”: {2} i jeszcze {3}.", "Type_0_is_not_a_constructor_function_type_2507": "Typ „{0}” nie jest typem funkcji konstruktora.", "Type_0_is_not_a_valid_async_function_return_type_in_ES5_because_it_does_not_refer_to_a_Promise_compa_1055": "Typ „{0}” nie jest prawidłowym zwracanym typem funkcji asynchronicznej w języku ES5, ponieważ nie odwołuje się do wartości konstruktora zgodnej z elementem Promise.", "Type_0_is_not_an_array_type_2461": "Typ „{0}” nie jest typem tablicowym.", "Type_0_is_not_an_array_type_or_a_string_type_2495": "Typ „{0}” nie jest typem tablicowym ani typem ciągu.", "Type_0_is_not_an_array_type_or_a_string_type_or_does_not_have_a_Symbol_iterator_method_that_returns__2549": "<PERSON>p „{0}” nie jest typem tablicy ani ciągu lub nie ma metody „[Symbol.iterator]()” zwracającej iterator.", "Type_0_is_not_an_array_type_or_does_not_have_a_Symbol_iterator_method_that_returns_an_iterator_2548": "<PERSON>p „{0}” nie jest typem tablicy lub nie ma metody „[Symbol.iterator]()” zwracającej iterator.", "Type_0_is_not_assignable_to_type_1_2322": "<PERSON><PERSON> „{0}” nie można przypisać do typu „{1}”.", "Type_0_is_not_assignable_to_type_1_Did_you_mean_2_2820": "Nie można przypisać typu „{0}” do typu „{1}”. <PERSON><PERSON>ził<PERSON> o „{2}”?", "Type_0_is_not_assignable_to_type_1_Two_different_types_with_this_name_exist_but_they_are_unrelated_2719": "Typu „{0}” nie można przypisać do typu „{1}”. Istnieją dwa różne typy o tej nazwie, lecz są ze sobą niezwiązane.", "Type_0_is_not_assignable_to_type_1_as_implied_by_variance_annotation_2636": "<PERSON>e można przypisać typu „{0}” do typu „{1}”, jak sugeru<PERSON> ad<PERSON>.", "Type_0_is_not_assignable_to_type_1_as_required_for_computed_enum_member_values_18033": "Nie można przypisać typu „{0}” do typu „{1}” zgodnie z wymaganiami dla obliczonych wartości składowych wyliczenia.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2375": "Nie można przypisać typu \"{0}\" do typu \"{1}\" o wartości \"exactOptionalPropertyTypes: true\". Rozważ dodanie elementu \"undefined\" do typów właściwości obiektu docelowego.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2412": "Nie można przypisać typu \"{0}\" do typu \"{1}\" o wartości \"exactOptionalPropertyTypes: true\". Rozważ dodanie elementu \"undefined\" do typu elementu docelowego.", "Type_0_is_not_comparable_to_type_1_2678": "<PERSON><PERSON> „{0}” nie można porównać z typem „{1}”.", "Type_0_is_not_generic_2315": "<PERSON><PERSON> „{0}” nie jest og<PERSON>.", "Type_0_may_represent_a_primitive_value_which_is_not_permitted_as_the_right_operand_of_the_in_operato_2638": "Typ „{0}” może reprezentować wartość pierwotn<PERSON>, co nie jest dozwolone jako prawy operand operatora „in”.", "Type_0_must_have_a_Symbol_asyncIterator_method_that_returns_an_async_iterator_2504": "Typ „{0}” musi zawiera<PERSON> metodę „[Symbol.asyncIterator]()” zwracającą iterator asynchroniczny.", "Type_0_must_have_a_Symbol_iterator_method_that_returns_an_iterator_2488": "Typ „{0}” musi zawiera<PERSON> metodę „[Symbol.iterator]()” zwracającą iterator.", "Type_0_provides_no_match_for_the_signature_1_2658": "<PERSON><PERSON> „{0}” nie udostępnia dopasowania dla sygnatury „{1}”.", "Type_0_recursively_references_itself_as_a_base_type_2310": "Typ „{0}” rekursywnie przywołuje sam siebie jako typ podstawowy.", "Type_Checking_6248": "Sprawd<PERSON><PERSON> typu", "Type_alias_0_circularly_references_itself_2456": "<PERSON><PERSON> ty<PERSON> „{0}” cyklicznie przywołuje sam siebie.", "Type_alias_must_be_given_a_name_1439": "Alias typu musi mieć nazwę.", "Type_alias_name_cannot_be_0_2457": "<PERSON>as typu nie może mieć nazwy „{0}”.", "Type_aliases_can_only_be_used_in_TypeScript_files_8008": "Aliasów typów można używać tylko w plikach TypeScript.", "Type_annotation_cannot_appear_on_a_constructor_declaration_1093": "Adnotacja typu nie może występować w deklaracji konstruktora.", "Type_annotations_can_only_be_used_in_TypeScript_files_8010": "Adnotacji typu można używać tylko w plikach TypeScript.", "Type_argument_expected_1140": "Oczekiwano argumentu typu.", "Type_argument_list_cannot_be_empty_1099": "Lista argumentów typu nie może być pusta.", "Type_arguments_can_only_be_used_in_TypeScript_files_8011": "Argumenty typu mogą być używane tylko w plikach TypeScript.", "Type_arguments_for_0_circularly_reference_themselves_4109": "Argumenty typu dla elementu „{0}” cyklicznie odwołują się do samych siebie.", "Type_assertion_expressions_can_only_be_used_in_TypeScript_files_8016": "Wyrażeń asercji typu można używać tylko w plikach TypeScript.", "Type_at_position_0_in_source_is_not_compatible_with_type_at_position_1_in_target_2626": "Typ na pozycji {0} w <PERSON><PERSON><PERSON><PERSON> nie jest zgodny z typem na pozycji {1} w lokalizacji docelowej.", "Type_at_positions_0_through_1_in_source_is_not_compatible_with_type_at_position_2_in_target_2627": "Typ na pozycjach od {0} do {1} w ź<PERSON><PERSON><PERSON> nie jest zgodny z typem w pozycji {2} w lokalizacji docelowej.", "Type_containing_private_name_0_can_t_be_used_with_isolatedDeclarations_9039": "Typ zawierający nazwę prywatną „{0}” nie może być używany z elementem --isolatedDeclarations.", "Type_declaration_files_to_be_included_in_compilation_6124": "Pliki deklaracji typu do uwzględnienia w kompilacji.", "Type_expected_1110": "Oczekiwano typu.", "Type_import_assertions_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1456": "Twierdzenie importu typu powinno mieć dokładnie jeden klucz – „resolution-mode“ – z wartością „importuj“ lub „wymagaj“.", "Type_import_attributes_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1464": "Atrybuty importu typów powinny mieć dokładnie jeden klucz — „resolution-mode” — z wartością „import” lub „require”.", "Type_instantiation_is_excessively_deep_and_possibly_infinite_2589": "Tworzenie wystąpienia typu jest nadmiernie szczegółowe i prawdopodobnie nieskończone.", "Type_is_referenced_directly_or_indirectly_in_the_fulfillment_callback_of_its_own_then_method_1062": "Typ jest przywoływany bezpośrednio lub pośrednio w wywołaniu zwrotnym realizacji jego własnej metody „then”.", "Type_library_referenced_via_0_from_file_1_1402": "Biblioteka typów jest przywoływana za pośrednictwem elementu „{0}” z pliku „{1}”", "Type_library_referenced_via_0_from_file_1_with_packageId_2_1403": "Biblioteka typów jest przywoływana za pośrednictwem elementu „{0}” z pliku „{1}” o identyfikatorze packageId „{2}”", "Type_of_await_operand_must_either_be_a_valid_promise_or_must_not_contain_a_callable_then_member_1320": "Typ operandu „await” musi być prawidłową obietnicą lub nie może zawierać wywoływalnej składowej „then”.", "Type_of_computed_property_s_value_is_0_which_is_not_assignable_to_type_1_2418": "Typ wartości właściwości obliczanej to „{0}”, którego nie można przypisać do typu „{1}”.", "Type_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2844": "Typ zmiennej składowej wystąpienia „{0}” nie może odwoływać się do identyfikatora „{1}” zadeklarowanego w konstruktorze.", "Type_of_iterated_elements_of_a_yield_Asterisk_operand_must_either_be_a_valid_promise_or_must_not_con_1322": "Typ iterowanych elementów operandu „yield*” musi być prawidł<PERSON>ą obietnicą lub nie może zawierać wywoływalnej składowej „then”.", "Type_of_property_0_circularly_references_itself_in_mapped_type_1_2615": "Typ właści<PERSON>ści „{0}” cyklicznie odwołuje się do siebie w zamapowanym typie „{1}”.", "Type_of_yield_operand_in_an_async_generator_must_either_be_a_valid_promise_or_must_not_contain_a_cal_1321": "Typ operandu „yield” w generatorze asynchronicznym musi być prawidł<PERSON>ą obietnicą lub nie może zawierać wywoływalnej składowej „then”.", "Type_originates_at_this_import_A_namespace_style_import_cannot_be_called_or_constructed_and_will_cau_7038": "Typ pochodzi z tego importu. Nie można wywołać ani skonstruować importu w stylu przestrzeni nazw, co spowoduje wystąpienie błędu w czasie wykonywania. Zamiast tego rozważ użycie importu domyślnego lub funkcji require importu.", "Type_parameter_0_has_a_circular_constraint_2313": "Parametr typu „{0}” zawiera ograniczenie cykliczne.", "Type_parameter_0_has_a_circular_default_2716": "Parametr typu „{0}” ma cykliczną wartość domyślną.", "Type_parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4008": "Parametr typu „{0}” sygnatury wywołania z wyeksportowanego interfejsu ma nazwę prywatną „{1}” lub używa tej nazwy.", "Type_parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4006": "Parametr typu „{0}” sygnatury konstruktora z wyeksportowanego interfejsu ma nazwę prywatną „{1}” lub używa tej nazwy.", "Type_parameter_0_of_exported_class_has_or_is_using_private_name_1_4002": "Parametr typu „{0}” wyeksportowanej klasy ma nazwę prywatną „{1}” lub używa tej nazwy.", "Type_parameter_0_of_exported_function_has_or_is_using_private_name_1_4016": "Parametr typu „{0}” wyeksportowanej funkcji ma nazwę prywatną „{1}” lub używa tej nazwy.", "Type_parameter_0_of_exported_interface_has_or_is_using_private_name_1_4004": "Parametr typu „{0}” wyeksportowanego interfejsu ma nazwę prywatną „{1}” lub używa tej nazwy.", "Type_parameter_0_of_exported_mapped_object_type_is_using_private_name_1_4103": "Parametr typu „{0}” wyeksportowanego mapowanego typu obiektu używa nazwy prywatnej „{1}”.", "Type_parameter_0_of_exported_type_alias_has_or_is_using_private_name_1_4083": "Parametr typu „{0}” wyeksportowanego aliasu typu ma nazwę prywatną „{1}” lub jej <PERSON>.", "Type_parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4014": "Parametr typu „{0}” metody z wyeksportowanego interfejsu ma nazwę prywatną „{1}” lub używa tej nazwy.", "Type_parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4012": "Parametr typu „{0}” metody publicznej z wyeksportowanej klasy ma nazwę prywatną „{1}” lub używa tej nazwy.", "Type_parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4010": "Parametr typu „{0}” publicznej metody statycznej z wyeksportowanej klasy ma nazwę prywatną „{1}” lub używa tej nazwy.", "Type_parameter_declaration_expected_1139": "Oczekiwano deklaracji parametru typu.", "Type_parameter_declarations_can_only_be_used_in_TypeScript_files_8004": "Deklaracje parametrów typu mogą być używane tylko w plikach TypeScript.", "Type_parameter_defaults_can_only_reference_previously_declared_type_parameters_2744": "Wartości domyślne parametrów typu mogą się odwoływać tylko do wcześniej zadeklarowanych parametrów typu.", "Type_parameter_list_cannot_be_empty_1098": "Lista parametrów typu nie może być pusta.", "Type_parameter_name_cannot_be_0_2368": "Parametr typu nie może mieć nazwy „{0}”.", "Type_parameters_cannot_appear_on_a_constructor_declaration_1092": "Parametry typu nie mogą występować w deklaracji konstruktora.", "Type_predicate_0_is_not_assignable_to_1_1226": "Predykatu typów „{0}” nie można przypisać do elementu „{1}”.", "Type_produces_a_tuple_type_that_is_too_large_to_represent_2799": "<PERSON>p <PERSON><PERSON>y typ krot<PERSON>, k<PERSON><PERSON><PERSON> jest zbyt du<PERSON>, aby go reprez<PERSON><PERSON>.", "Type_reference_directive_0_was_not_resolved_6120": "======== Dyrektywa odwołania do typu „{0}” nie została rozpoznana. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_primary_Colon_2_6119": "======== Dyrektywa odwołania do typu „{0}” została pomyślnie rozpoznana jako „{1}”, podstawowe: {2}. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_with_Package_ID_2_primary_Colon_3_6219": "======== Dyrektywa odwołania do typu „{0}” została pomyślnie rozpoznana jako „{1}” z identyfikatorem pakietu „{2}”, podstawowe: {3}. ========", "Type_satisfaction_expressions_can_only_be_used_in_TypeScript_files_8037": "Wyrażeń asercji typu można używać tylko w plikach TypeScript.", "Types_cannot_appear_in_export_declarations_in_JavaScript_files_18043": "Typy nie mogą występować w deklaracjach eksportu w plikach JavaScript.", "Types_have_separate_declarations_of_a_private_property_0_2442": "Ty<PERSON> mają osobne deklaracje właściwości prywatnej „{0}”.", "Types_of_construct_signatures_are_incompatible_2419": "Typy sygnatur konstrukcji są niezgodne.", "Types_of_parameters_0_and_1_are_incompatible_2328": "<PERSON><PERSON> parametrów „{0}” i „{1}” są niezgodne.", "Types_of_property_0_are_incompatible_2326": "<PERSON><PERSON> w<PERSON> „{0}” są niezgodne.", "Unable_to_open_file_0_6050": "Nie można otworzyć pliku „{0}”.", "Unable_to_resolve_signature_of_class_decorator_when_called_as_an_expression_1238": "Nie można rozpoznać sygnatury dekoratora klasy wywołanego jako wyrażenie.", "Unable_to_resolve_signature_of_method_decorator_when_called_as_an_expression_1241": "<PERSON>e można rozpoznać sygnatury dekoratora metody wywołanego jako wyrażenie.", "Unable_to_resolve_signature_of_parameter_decorator_when_called_as_an_expression_1239": "Nie można rozpoznać sygnatury dekoratora parametru wywołanego jako wyrażenie.", "Unable_to_resolve_signature_of_property_decorator_when_called_as_an_expression_1240": "Nie można rozpoznać sygnatury dekoratora właściwości wywołanego jako wyrażenie.", "Undetermined_character_escape_1513": "Nieokreślony znak ucieczki.", "Unexpected_0_Did_you_mean_to_escape_it_with_backslash_1508": "Nieoczekiwane „{0}”. <PERSON><PERSON> chodziło Ci o ucieczkę za pomocą ukośnika odwrotnego?", "Unexpected_end_of_text_1126": "Nieoczekiwany koniec tekstu.", "Unexpected_keyword_or_identifier_1434": "Nieoczekiwane słowo kluczowe lub identyfikator.", "Unexpected_token_1012": "Nieoczekiwany token.", "Unexpected_token_A_constructor_method_accessor_or_property_was_expected_1068": "Nieoczekiwany token. Oczek<PERSON><PERSON><PERSON> k<PERSON>, metody, metody dostępu lub w<PERSON><PERSON><PERSON><PERSON><PERSON>.", "Unexpected_token_A_type_parameter_name_was_expected_without_curly_braces_1069": "Nieoczekiwany token. Oczekiwano nazwy parametru typu bez nawiasów klamrowych.", "Unexpected_token_Did_you_mean_or_gt_1382": "Nieoczekiwany token. <PERSON><PERSON> ch<PERSON> o „{'>'}” lub „&gt;”?", "Unexpected_token_Did_you_mean_or_rbrace_1381": "Nieoczekiwany token. <PERSON><PERSON> ch<PERSON> o „{'}'}” lub „&rbrace;”?", "Unexpected_token_expected_1179": "Nieoczekiwany token. Oczekiwano znaku „{”.", "Unicode_escape_sequence_cannot_appear_here_17021": "W tym miejscu nie można wyświetlić sekwencji ucieczki standardu Unicode.", "Unicode_escape_sequences_are_only_available_when_the_Unicode_u_flag_or_the_Unicode_Sets_v_flag_is_se_1538": "Sekwencje ucieczki standardu Unicode są dostępne tylko wtedy, gdy ustawiono flagę Unicode (u) lub flagę Unicode Sets (v).", "Unicode_property_value_expressions_are_only_available_when_the_Unicode_u_flag_or_the_Unicode_Sets_v__1530": "Wyrażenia wartości właściwości standardu Unicode są dostępne tylko wtedy, gdy ustawiono flagę Unicode (u) lub flagę Unicode Sets (v).", "Unknown_Unicode_property_name_1524": "Nieznana nazwa właściwości standardu Unicode.", "Unknown_Unicode_property_name_or_value_1529": "Nieznana nazwa lub warto<PERSON> właściwości standardu Unicode.", "Unknown_Unicode_property_value_1526": "<PERSON><PERSON><PERSON><PERSON> właściwości standardu Unicode.", "Unknown_build_option_0_5072": "Nieznana opcja kompilacji „{0}”.", "Unknown_build_option_0_Did_you_mean_1_5077": "Nieznana opcja kompilacji „{0}”. <PERSON><PERSON> o „{1}”?", "Unknown_compiler_option_0_5023": "Nieznana opcja kompilatora „{0}”.", "Unknown_compiler_option_0_Did_you_mean_1_5025": "Nieznana opcja kompilatora „{0}”. <PERSON><PERSON> o „{1}”?", "Unknown_keyword_or_identifier_Did_you_mean_0_1435": "Nieznane słowo kluczowe lub identyfikator. <PERSON><PERSON>i o „{0}”?", "Unknown_option_excludes_Did_you_mean_exclude_6114": "<PERSON><PERSON><PERSON><PERSON> op<PERSON>ja „excludes”. <PERSON><PERSON> o „exclude”?", "Unknown_regular_expression_flag_1499": "Nieznana flaga wyrażenia regularnego.", "Unknown_type_acquisition_option_0_17010": "Opcja pozyskania nieznanego typu „{0}”.", "Unknown_type_acquisition_option_0_Did_you_mean_1_17018": "Nieznana opcja pozyskania typu „{0}”. <PERSON><PERSON> o „{1}”?", "Unknown_watch_option_0_5078": "Nieznana opcja obserwowania „{0}”.", "Unknown_watch_option_0_Did_you_mean_1_5079": "Nieznana opcja obserwowania: „{0}”. <PERSON><PERSON> o „{1}”?", "Unreachable_code_detected_7027": "Wykryto nieosiągalny kod.", "Unterminated_Unicode_escape_sequence_1199": "Niezakończona sekwencja ucieczki kodu Unicode.", "Unterminated_quoted_string_in_response_file_0_6045": "Niezakończony ciąg ujęty w cudzysłów w pliku odpowiedzi „{0}”.", "Unterminated_regular_expression_literal_1161": "Niezakończony literał wyrażenia regularnego.", "Unterminated_string_literal_1002": "Niezakończony literał ciągu znaków.", "Unterminated_template_literal_1160": "Niezakończony literał szablonu.", "Untyped_function_calls_may_not_accept_type_arguments_2347": "Wywołania funkcji bez typu nie mogą przyjmować argumentów typu.", "Unused_label_7028": "Nieużywana etykieta.", "Unused_ts_expect_error_directive_2578": "Nieużywana dyrektywa „@ts-expect-error”.", "Update_import_from_0_90058": "Aktualizuj import z „{0}”", "Update_modifiers_of_0_90061": "Modyfikatory aktualizacji elementu „{0}”", "Updating_output_timestamps_of_project_0_6359": "Trwa aktualizowanie sygnatury czasowej danych wyjściowych projektu „{0}”...", "Updating_unchanged_output_timestamps_of_project_0_6371": "Trwa aktualizowanie niezmienionych sygnatur czasowych danych wyjściowych projektu „{0}”...", "Use_0_95174": "<PERSON><PERSON><PERSON><PERSON> „{0}”.", "Use_0_instead_5106": "Zamiast tego uż<PERSON>j „{0}”.", "Use_Number_isNaN_in_all_conditions_95175": "Użyj wartości „Number.isNaN” we wszystkich warunkach.", "Use_element_access_for_0_95145": "Użyj dostępu do elementu w przypadku elementu „{0}”", "Use_element_access_for_all_undeclared_properties_95146": "Użyj dostępu do elementu w przypadku wszystkich niezadeklarowanych właściwości.", "Use_import_type_95180": "Użyj opcji „import type”", "Use_synthetic_default_member_95016": "Użyj syntetycznej składowej „default”.", "Use_the_package_json_exports_field_when_resolving_package_imports_6408": "Podczas rozpoznawania importów pakietów użyj pola „exports” pliku package.json.", "Use_the_package_json_imports_field_when_resolving_imports_6409": "Podczas rozpoznawania importów użyj pola „imports” pliku package.json.", "Use_type_0_95181": "<PERSON><PERSON><PERSON><PERSON> „typu {0}”", "Using_0_subpath_1_with_target_2_6404": "Używanie „{0}” ścieżki podrzędnej „{1}” z elementem docelowym „{2}”.", "Using_a_string_in_a_for_of_statement_is_only_supported_in_ECMAScript_5_and_higher_2494": "Używanie ciągu w instrukcji „for...of” jest obsługiwane tylko w języku ECMAScript 5 lub nowszym.", "Using_build_b_will_make_tsc_behave_more_like_a_build_orchestrator_than_a_compiler_This_is_used_to_tr_6915": "Użycie elementu --build, -b s<PERSON><PERSON>, że narzędzie tsc będzie zachowywało się bardziej jak orkiestrator kompilacji niż kompilator. Ta opcja jest wykorzystywana do wyzwalania kompilacji projektów złożonych, o których dowiesz się więcej na stronie {0}", "Using_compiler_options_of_project_reference_redirect_0_6215": "Using compiler options of project reference redirect '{0}'.", "VERSION_6036": "WERSJA", "Value_of_type_0_has_no_properties_in_common_with_type_1_Did_you_mean_to_call_it_2560": "<PERSON><PERSON><PERSON><PERSON> typu „{0}” nie ma żadnych wspólnych właściwości z typem „{1}”. <PERSON><PERSON> jej wywołanie było zamierzone?", "Value_of_type_0_is_not_callable_Did_you_mean_to_include_new_2348": "Nie można wywołać wartości typu „{0}”. <PERSON><PERSON> miał zostać użyty operator „new”?", "Variable_0_implicitly_has_an_1_type_7005": "<PERSON><PERSON> „{0}” niejawnie określono typ „{1}”.", "Variable_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7043": "Z<PERSON>nna „{0}” niej<PERSON>nie ma typ „{1}”, ale lepszy typ można wywnioskować na podstawie użycia.", "Variable_0_implicitly_has_type_1_in_some_locations_but_a_better_type_may_be_inferred_from_usage_7046": "Zmienna „{0}” niejawnie ma typ „{1}” w niektórych lokalizacjach, ale lepszy typ można wywnioskować na podstawie użycia.", "Variable_0_implicitly_has_type_1_in_some_locations_where_its_type_cannot_be_determined_7034": "<PERSON><PERSON><PERSON> „{0}” ma niejawnie typ „{1}” w niektórych lokalizacjach, w których nie można określić jej typu.", "Variable_0_is_used_before_being_assigned_2454": "Z<PERSON>nna „{0}” jest używana przed przypisaniem.", "Variable_declaration_expected_1134": "Oczekiwano deklaracji zmiennej.", "Variable_declaration_list_cannot_be_empty_1123": "Lista deklaracji zmiennych nie może być pusta.", "Variable_declaration_not_allowed_at_this_location_1440": "Deklaracja zmiennej jest niedozwolona w tej lokalizacji.", "Variable_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9010": "Zmienna musi mieć jawną adnotację typu z właściwością --isolatedDeclarations.", "Variables_with_multiple_declarations_cannot_be_inlined_95186": "Zmienne z wieloma deklaracjami nie mogą być śródwierszowe.", "Variadic_element_at_position_0_in_source_does_not_match_element_at_position_1_in_target_2625": "Element ze zmienną liczbą argumentów na pozycji {0} w ź<PERSON><PERSON><PERSON> nie jest zgodny z elementem na pozycji {1} w lokalizacji docelowej.", "Variance_annotations_are_only_supported_in_type_aliases_for_object_function_constructor_and_mapped_t_2637": "Adnotacje wariancji są obsługiwane tylko w aliasach typów obiektów, funkcji, konstruktorów i mapowanych typów.", "Version_0_6029": "<PERSON><PERSON><PERSON> {0}", "Visit_https_Colon_Slash_Slashaka_ms_Slashtsconfig_to_read_more_about_this_file_95110": "<PERSON><PERSON><PERSON><PERSON><PERSON> https://aka.ms/tsconfig, aby dowied<PERSON> się więcej o tym pliku", "WATCH_OPTIONS_6918": "OPCJE OBSERWACJI", "Watch_and_Build_Modes_6250": "Tryb wyrażeń kontrolnych i kompilowania", "Watch_input_files_6005": "Obserwuj pliki wejściowe.", "Watch_option_0_requires_a_value_of_type_1_5080": "Opcja obserwowania „{0}” wymaga wartości typu {1}.", "We_can_only_write_a_type_for_0_by_adding_a_type_for_the_entire_parameter_here_2843": "<PERSON><PERSON><PERSON><PERSON> typ tylko dla elementu „{0}”, doda<PERSON><PERSON>c typ dla całego parametru w tym <PERSON>.", "When_assigning_functions_check_to_ensure_parameters_and_the_return_values_are_subtype_compatible_6698": "Podczas przypisywania funkcji upewnij się, że parametry i zwracane wartości są zgodne pod względem podtypów.", "When_type_checking_take_into_account_null_and_undefined_6699": "Podczas sprawdzania typów uwzględniaj wartości „null” i „undefined”.", "Whether_to_keep_outdated_console_output_in_watch_mode_instead_of_clearing_the_screen_6191": "<PERSON><PERSON><PERSON><PERSON>, c<PERSON> zachować nieaktualne dane wyjściowe konsoli w trybie śledzenia zamiast wyczyścić ekran.", "Wrap_all_invalid_characters_in_an_expression_container_95109": "Opakuj wszystkie nieprawidłowe znaki w kontenerze wyrażenia", "Wrap_all_invalid_decorator_expressions_in_parentheses_95195": "Zawijaj wszystkie nieprawidłowe wyrażenia dekoratora w nawiasy", "Wrap_all_object_literal_with_parentheses_95116": "Zawijaj wszystkie literały obiektu z nawiasami", "Wrap_all_unparented_JSX_in_JSX_fragment_95121": "Opakuj wszystkie elementy JSX bez elementu nadrzędnego we fragment JSX", "Wrap_in_JSX_fragment_95120": "Opakuj we fragment JSX", "Wrap_in_parentheses_95194": "Zawijaj w nawiasy", "Wrap_invalid_character_in_an_expression_container_95108": "Opakuj nieprawidłowy znak w kontenerze wyrażenia", "Wrap_the_following_body_with_parentheses_which_should_be_an_object_literal_95113": "Zawijaj następującą treść z nawiasami, która powinna być literałem obiektu", "You_can_learn_about_all_of_the_compiler_options_at_0_6913": "O wszystkich opcjach kompilatora przeczytasz na stronie {0}", "You_cannot_rename_a_module_via_a_global_import_8031": "Nie można zmienić nazwy modułu za pomocą importu globalnego.", "You_cannot_rename_elements_that_are_defined_in_a_node_modules_folder_8035": "Nie można zmieniać nazw elementów zdefiniowanych w folderze „node_modules”.", "You_cannot_rename_elements_that_are_defined_in_another_node_modules_folder_8036": "Nie można zmieniać nazw elementów zdefiniowanych w innym folderze „node_modules”.", "You_cannot_rename_elements_that_are_defined_in_the_standard_TypeScript_library_8001": "Nie można zmienić nazw elementów zdefiniowanych w standardowej bibliotece TypeScript.", "You_cannot_rename_this_element_8000": "Nie można zmienić nazwy tego elementu.", "_0_accepts_too_few_arguments_to_be_used_as_a_decorator_here_Did_you_mean_to_call_it_first_and_write__1329": "Element „{0}” akceptuje za mało argumentów, aby można go było u<PERSON> w tym miejscu jako dekorator. <PERSON><PERSON> chcesz najpierw go wywołać i zapisać tag „@{0}()”?", "_0_and_1_index_signatures_are_incompatible_2330": "Sygnatury indeksów „{0}” i „{1}” są niezgodne.", "_0_and_1_operations_cannot_be_mixed_without_parentheses_5076": "<PERSON><PERSON><PERSON> „{0}” i „{1}” nie można mieszać bez nawiasów.", "_0_are_specified_twice_The_attribute_named_0_will_be_overwritten_2710": "Element „{0}” został określony dwa razy. Atrybut o nazwie „{0}” zostanie przesłonięty.", "_0_at_the_end_of_a_type_is_not_valid_TypeScript_syntax_Did_you_mean_to_write_1_17019": "Element „{0}” na końcu typu nie jest prawidłową składnią języka TypeScript. <PERSON>zy chodziło Ci o napisanie „{1}”?", "_0_at_the_start_of_a_type_is_not_valid_TypeScript_syntax_Did_you_mean_to_write_1_17020": "Element „{0}” na początku typu nie jest prawidłową składnią języka TypeScript. Czy chodziło Ci o napisanie „{1}”?", "_0_can_only_be_imported_by_turning_on_the_esModuleInterop_flag_and_using_a_default_import_2596": "Element „{0}” można zaimportować tylko przez włączenie flagi „esModuleInterop” i użycie importu domyślnego.", "_0_can_only_be_imported_by_using_a_default_import_2595": "Element „{0}” można zaimportować tylko przy użyciu importu domyślnego.", "_0_can_only_be_imported_by_using_a_require_call_or_by_turning_on_the_esModuleInterop_flag_and_using__2598": "Element „{0}” można zaimportować za pomocą wywołania „require” lub przez włączenie flagi „esModuleInterop” i użycie importu domyślnego.", "_0_can_only_be_imported_by_using_a_require_call_or_by_using_a_default_import_2597": "Element „{0}” można zaimportować tylko za pomocą wywołania „require” lub przy użyciu importu domyślnego.", "_0_can_only_be_imported_by_using_import_1_require_2_or_a_default_import_2616": "Element „{0}” można zaimportować tylko przy użyciu wyrażenia „import {1} = require({2})” lub importu domyślnego.", "_0_can_only_be_imported_by_using_import_1_require_2_or_by_turning_on_the_esModuleInterop_flag_and_us_2617": "Element „{0}” można zaimportować tylko przy użyciu wyrażenia „import {1} = require({2})” lub przez włączenie flagi „esModuleInterop” i użycie importu domyślnego.", "_0_cannot_be_used_as_a_JSX_component_2786": "Elementu „{0}” nie można użyć jako składnika JSX.", "_0_cannot_be_used_as_a_value_because_it_was_exported_using_export_type_1362": "Element „{0}” nie może by<PERSON> uż<PERSON>wan<PERSON> jako <PERSON>, ponieważ został wyeksportowany przy użyciu aliasu „export type”.", "_0_cannot_be_used_as_a_value_because_it_was_imported_using_import_type_1361": "Element „{0}” nie może by<PERSON> uż<PERSON>wan<PERSON> jako <PERSON>, ponieważ został zaimportowany przy użyciu aliasu „import type”.", "_0_components_don_t_accept_text_as_child_elements_Text_in_JSX_has_the_type_string_but_the_expected_t_2747": "Składniki „{0}” nie akceptują tekstu jako elementów podrzędnych. Tekst w JSX ma typ „string”, ale oczekiwany typ elementu „{1}” to „{2}”.", "_0_could_be_instantiated_with_an_arbitrary_type_which_could_be_unrelated_to_1_5082": "Nie można utworzyć wystąpienia elementu „{0}” z dowolnym typem, który może być niezwiązany z elementem „{1}”.", "_0_declarations_can_only_be_declared_inside_a_block_1156": "<PERSON><PERSON><PERSON><PERSON> „{0}” mogą być deklarowane tylko w bloku.", "_0_declarations_can_only_be_used_in_TypeScript_files_8006": "<PERSON><PERSON><PERSON><PERSON> „{0}” mogą być używane tylko w plikach TypeScript.", "_0_declarations_may_not_have_binding_patterns_1492": "<PERSON><PERSON><PERSON><PERSON> „{0}” mogą nie mieć wzorców powiązań.", "_0_declarations_must_be_initialized_1155": "<PERSON><PERSON><PERSON><PERSON> „{0}” muszą by<PERSON>.", "_0_expected_1005": "Oczekiwano elementu „{0}”.", "_0_has_a_string_type_but_must_have_syntactically_recognizable_string_syntax_when_isolatedModules_is__18055": "Element „{0}” ma typ ciągu, ale po włączeniu opcji „isolatedModules” musi mieć składnię ciągu rozpoznawalną składniowo.", "_0_has_no_exported_member_named_1_Did_you_mean_2_2724": "Element „{0}” nie ma wyeksportowanego elementu członkowskiego „{1}”. <PERSON><PERSON> o „{2}”?", "_0_implicitly_has_an_1_return_type_but_a_better_type_may_be_inferred_from_usage_7050": "Element „{0}” niejawnie ma zwracany typ „{1}”, ale lepszy typ można wywnioskować na podstawie użycia.", "_0_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_reference_7023": "Dla elementu „{0}” niejawnie określono zwracany typ „any”, ponieważ nie zawiera on adnotacji zwracanego typu i jest przywoływany bezpośrednio lub pośrednio w jednym z jego zwracanych wyrażeń.", "_0_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_and_is_referenced_directly_or__7022": "Dla elementu „{0}” niejawnie określono typ „any”, ponieważ nie zawiera on adnotacji typu i jest przywoływany bezpośrednio lub pośrednio w jego własnym inicjatorze.", "_0_index_signatures_are_incompatible_2634": "Sygnatury indeksów „{0}” są niezgodne.", "_0_index_type_1_is_not_assignable_to_2_index_type_3_2413": "Typu indeksu „{0}” „{1}” nie można przypisać do typu indeksu „{2}” „{3}”.", "_0_is_a_primitive_but_1_is_a_wrapper_object_Prefer_using_0_when_possible_2692": "Element „{0}” jest elementem podstawowym, ale element „{1}” jest obiektem otoki. Preferuje się użycie elementu „{0}”, jeśli jest to możliwe.", "_0_is_a_type_and_cannot_be_imported_in_JavaScript_files_Use_1_in_a_JSDoc_type_annotation_18042": "„{0}” jest typem i nie można go zaimportować w plikach JavaScript. Użyj elementu „{1}” w adnotacji typu JSDoc.", "_0_is_a_type_and_must_be_imported_using_a_type_only_import_when_verbatimModuleSyntax_is_enabled_1484": "Element „{0}” jest typem i należy go zaimportować przy użyciu importu tylko typu, gdy jest włączona opcja „verbatimModuleSyntax”.", "_0_is_an_unused_renaming_of_1_Did_you_intend_to_use_it_as_a_type_annotation_2842": "„{0}” to nieużywana zmiana nazwy elementu „{1}”. <PERSON><PERSON>żywać go jako adnotacji typu?", "_0_is_assignable_to_the_constraint_of_type_1_but_1_could_be_instantiated_with_a_different_subtype_of_5075": "Element „{0}” można przypisać do ograniczenia typu „{1}”, ale wystąpienie typu „{1}” można utworzyć z innym podtypem ograniczenia „{2}”.", "_0_is_automatically_exported_here_18044": "W tym miej<PERSON>cu jest automatycznie eksportowany element „{0}”.", "_0_is_declared_but_its_value_is_never_read_6133": "Element „{0}” jest <PERSON><PERSON>, ale jego warto<PERSON> nie jest nigdy odczyty<PERSON>.", "_0_is_declared_but_never_used_6196": "Element „{0}” jest <PERSON>, ale nie jest nigdy <PERSON>.", "_0_is_declared_here_2728": "Element „{0}” jest zadeklarowany tutaj.", "_0_is_defined_as_a_property_in_class_1_but_is_overridden_here_in_2_as_an_accessor_2611": "Element „{0}” jest zdefiniowany jako w<PERSON><PERSON> w klasie „{1}”, ale jest przesłaniany tutaj w elemencie „{2}” jako metoda dostępu.", "_0_is_defined_as_an_accessor_in_class_1_but_is_overridden_here_in_2_as_an_instance_property_2610": "Element „{0}” jest zdefiniowany jako metoda dostępu w klasie „{1}”, ale jest przesłaniany tutaj w elemencie „{2}” jako w<PERSON> wystąpienia.", "_0_is_deprecated_6385": "Element „{0}” jest prz<PERSON>rz<PERSON><PERSON><PERSON>.", "_0_is_not_a_valid_meta_property_for_keyword_1_Did_you_mean_2_17012": "„{0}” nie jest prawidłową metawłaściwością słowa kluczowego „{1}”. <PERSON><PERSON> miał to być element „{2}”?", "_0_is_not_allowed_as_a_parameter_name_1390": "„{0}” jest niedozwolone jako nazwa parametru.", "_0_is_not_allowed_as_a_variable_declaration_name_1389": "Element „{0}” nie jest dozwolony jako nazwa deklaracji zmiennej.", "_0_is_of_type_unknown_18046": "Element „{0}” jest typu „nieznany”.", "_0_is_possibly_null_18047": "Element „{0}” prawdopodobnie ma wartość „null”.", "_0_is_possibly_null_or_undefined_18049": "Element „{0}” prawdopodo<PERSON><PERSON> ma <PERSON> „null” lub jest „niezdefiniowany”.", "_0_is_possibly_undefined_18048": "Element „{0}” jest prawdo<PERSON><PERSON><PERSON>nie „niezdefiniowany”.", "_0_is_referenced_directly_or_indirectly_in_its_own_base_expression_2506": "Element „{0}” jest przywoływany bezpośrednio lub pośrednio w jego własnym wyrażeniu podstawowym.", "_0_is_referenced_directly_or_indirectly_in_its_own_type_annotation_2502": "Element „{0}” jest przywoływany bezpośrednio lub pośrednio w jego własnej adnotacji typu.", "_0_is_specified_more_than_once_so_this_usage_will_be_overwritten_2783": "Element „{0}” został określony więcej niż raz, dlatego to użycie zostanie przesłonięte.", "_0_list_cannot_be_empty_1097": "Lista „{0}” nie może by<PERSON> pusta.", "_0_modifier_already_seen_1030": "Napotkan<PERSON> j<PERSON> modyfi<PERSON> „{0}”.", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_class_interface_or_type_alias_1274": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” może występować tylko w parametrze typu klasy, interfejsu lub aliasu typu", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_function_method_or_class_1277": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” może występować tylko w parametrze typu funkcji, metody lub klasy", "_0_modifier_cannot_appear_on_a_constructor_declaration_1089": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie może występować w deklaracji konstruktora.", "_0_modifier_cannot_appear_on_a_module_or_namespace_element_1044": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie może być stosowany w przypadku elementu przestrzeni nazw lub modułu.", "_0_modifier_cannot_appear_on_a_parameter_1090": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie może występować w parametrze.", "_0_modifier_cannot_appear_on_a_type_member_1070": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie może być stosowany w przypadku składowej typu.", "_0_modifier_cannot_appear_on_a_type_parameter_1273": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie może występować w parametrze typu", "_0_modifier_cannot_appear_on_a_using_declaration_1491": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie może występować w deklaracji „using”.", "_0_modifier_cannot_appear_on_an_await_using_declaration_1495": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie może występować w deklaracji „await using”.", "_0_modifier_cannot_appear_on_an_index_signature_1071": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie może być stosowany w przypadku sygnatury indeksu.", "_0_modifier_cannot_appear_on_class_elements_of_this_kind_1031": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie może występować w elementach klasy tego rodzaju.", "_0_modifier_cannot_be_used_here_1042": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie można użyć w tym <PERSON>.", "_0_modifier_cannot_be_used_in_an_ambient_context_1040": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie można użyć w otaczającym kontekście.", "_0_modifier_cannot_be_used_with_1_modifier_1243": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie można używać z modyfikatorem „{1}”.", "_0_modifier_cannot_be_used_with_a_private_identifier_18019": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” nie można używać z identyfikatorem prywatnym.", "_0_modifier_must_precede_1_modifier_1029": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{0}” musi występować przed modyfikatorem „{1}”.", "_0_must_be_followed_by_a_Unicode_property_value_expression_enclosed_in_braces_1531": "Po wyrażeniu „\\{0}” musi następować wyrażenie wartości właściwości standardu Unicode ujęte w nawiasy klamrowe.", "_0_needs_an_explicit_type_annotation_2782": "Element „{0}” wymaga jawnej adnotacji typu.", "_0_only_refers_to_a_type_but_is_being_used_as_a_namespace_here_2702": "Element „{0}” odwołuje się tylko do typu, ale jest używany tutaj jako przestrzeń nazw.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_2693": "Element „{0}” odwołuje się jedynie do typu, ale jest używany w tym miej<PERSON>cu jako <PERSON>.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Did_you_mean_to_use_1_in_0_2690": "Element „{0}” odwołuje się do typu, ale jest używany tutaj jako warto<PERSON>. <PERSON><PERSON> chodziło o użycie wyrażenia „{1} in {0}”?", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Do_you_need_to_change_your_target_library_2585": "Element \"{0}\" odwołuje się tylko do typu, ale jest używany tutaj jako war<PERSON>. <PERSON><PERSON> ch<PERSON>z zmienić bibliotekę docelową? Spróbuj zmienić opcję kompilatora \"lib\" na es2015 lub nowszą.", "_0_refers_to_a_UMD_global_but_the_current_file_is_a_module_Consider_adding_an_import_instead_2686": "„{0}” odnosi się do globalnego formatu UMD, ale bieżący plik jest modułem. Rozważ zamiast tego dodanie importu.", "_0_refers_to_a_value_but_is_being_used_as_a_type_here_Did_you_mean_typeof_0_2749": "Element „{0}” odwołuje się do wartości, ale jest używany tutaj jako typ. <PERSON><PERSON> chodziło o „typeof {0}”?", "_0_resolves_to_a_type_and_must_be_marked_type_only_in_this_file_before_re_exporting_when_1_is_enable_1291": "Element „{0}” jest rozpoznawany jako typ i musi być oznaczony jako tylko typ w tym pliku przed ponownym wyeksportowaniem po włączeniu opcji „{1}”. Rozważ użycie parametru „typ importu”, gdzie element „{0}” jest importowany.", "_0_resolves_to_a_type_and_must_be_marked_type_only_in_this_file_before_re_exporting_when_1_is_enable_1292": "Element „{0}” jest rozpoznawany jako typ i musi być oznaczony jako tylko typ w tym pliku przed ponownym wyeksportowaniem po włączeniu opcji „{1}”. Rozważ użycie parametru „export type { domyślnie {0} }”.", "_0_resolves_to_a_type_only_declaration_and_must_be_imported_using_a_type_only_import_when_verbatimMo_1485": "Element „{0}” jest rozpoznawany jako deklaracja tylko typu i musi zostać zaimportowany przy użyciu importu tylko typu, gdy jest włączona opcja „verbatimModuleSyntax”.", "_0_resolves_to_a_type_only_declaration_and_must_be_marked_type_only_in_this_file_before_re_exporting_1289": "Element „{0}” jest rozpoznawany jako deklaracja tylko typu i musi być oznaczony jako tylko typ w tym pliku przed ponownym eksportowaniem po włączeniu opcji „{1}”. Rozważ użycie parametru „typ importu”, gdzie element „{0}” jest importowany.", "_0_resolves_to_a_type_only_declaration_and_must_be_marked_type_only_in_this_file_before_re_exporting_1290": "Element „{0}” jest rozpoznawany jako deklaracja tylko typu i musi być oznaczony jako tylko typ w tym pliku przed ponownym eksportowaniem po włączeniu opcji „{1}”. Rozważ użycie parametru „export type { domyślnie {0} }”.", "_0_resolves_to_a_type_only_declaration_and_must_be_re_exported_using_a_type_only_re_export_when_1_is_1448": "Element „{0}” jest rozpoznawany jako deklaracja tylko typu i musi zostać ponownie wyeksportowany przy użyciu ponownego eksportowania tylko typu, gdy włączono opcję „{1}”.", "_0_should_be_set_inside_the_compilerOptions_object_of_the_config_json_file_6258": "Element \"{0}\" powinien by<PERSON> ustawiony wewnątrz obiektu \"compilerOptions\" pliku json konfiguracji", "_0_tag_already_specified_1223": "Tag „{0}” jest już <PERSON>.", "_0_was_also_declared_here_6203": "Element „{0}” został również zadeklarowany w tym miejscu.", "_0_was_exported_here_1377": "Element „{0}” został wyeksportowany tutaj.", "_0_was_imported_here_1376": "Element „{0}” został zaimportowany tutaj.", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_return_type_7010": "<PERSON><PERSON> element<PERSON> „{0}” bez adnotacji zwracanego typu niejawnie określono zwracany typ „{1}”.", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_yield_type_7055": "<PERSON><PERSON> element<PERSON> „{0}” bez adnotacji zwracanego typu niejawnie określono zwracany typ „{1}”.", "abstract_modifier_can_only_appear_on_a_class_method_or_property_declaration_1242": "Mody<PERSON><PERSON>or „abstract” może być stosowany jedynie w przypadku deklaracji klasy, metody lub w<PERSON><PERSON><PERSON>.", "accessor_modifier_can_only_appear_on_a_property_declaration_1275": "Modyfi<PERSON>or „accessor” może występować tylko w deklaracji właściwości.", "and_here_6204": "i tutaj.", "arguments_cannot_be_referenced_in_property_initializers_2815": "w inicjatorach właściwości nie można odwoływać się do \"arguments\".", "auto_Colon_Treat_files_with_imports_exports_import_meta_jsx_with_jsx_Colon_react_jsx_or_esm_format_w_1476": "„auto”: Traktuj pliki za pomocą importów, eksportów, import.meta, jsx (z jsx: react-jsx) lub formatu esm (z modułem: node16+) jako moduły.", "await_expression_cannot_be_used_inside_a_class_static_block_18037": "Nie można użyć wyrażenia „await” wewnątrz bloku statycznego klasy.", "await_expressions_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_fi_1375": "Wyrażenia „await” na najwyższym poziomie pliku są dozwolone tylko wtedy, gdy plik jest modułem, ale ten plik nie ma żadnych importów ani eksportów. Rozważ dodanie pustego elementu „export {}”, aby ten plik był modułem.", "await_expressions_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1308": "Wyrażenia „await” są dozwolone tylko w funkcjach asynchronicznych i na najwyższym poziomie modułów.", "await_expressions_cannot_be_used_in_a_parameter_initializer_2524": "Wyrażeń „await” nie można używać w inicjatorze parametru.", "await_has_no_effect_on_the_type_of_this_expression_80007": "Operator „await” nie ma wpływu na typ tego wyrażenia.", "await_using_statements_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_th_2853": "Instrukcje „await using” są dozwolone tylko na najwyższym poziomie pliku, gdy ten plik jest modułem, ale ten plik nie ma importów ani eksportów. Rozważ dodanie pustego wyrażenia „export {}”, aby <PERSON><PERSON><PERSON>ić ten plik modułem.", "await_using_statements_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_2852": "Instrukcje „await using” są dozwolone tylko w ramach funkcji asynchronicznych i na najwyższych poziomach modułów.", "await_using_statements_cannot_be_used_inside_a_class_static_block_18054": "Instrukcji „await using” nie można używać wewnątrz bloku statycznego klasy.", "baseUrl_option_is_set_to_0_using_this_value_to_resolve_non_relative_module_name_1_6106": "<PERSON><PERSON><PERSON> „baseUrl” ma ustawi<PERSON> warto<PERSON> „{0}”. Ta wartość zostanie użyta do rozpoznania innej niż względna nazwy modułu „{1}”.", "c_must_be_followed_by_an_ASCII_letter_1512": "Po wyrażeniu „\\c” musi następować litera ASCII.", "can_only_be_used_at_the_start_of_a_file_18026": "Elementu „#!” można użyć tylko na początku pliku.", "case_or_default_expected_1130": "Oczekiwano elementu „case” lub „default”.", "catch_or_finally_expected_1472": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> instrukcji „catch” lub „finally”.", "const_enum_member_initializer_was_evaluated_to_a_non_finite_value_2477": "Wynikiem obliczenia inicjatora składowej wyliczenia ze specyfikatorem „const” jest wartość nieskończona.", "const_enum_member_initializer_was_evaluated_to_disallowed_value_NaN_2478": "Wynikiem obliczenia inicjatora składowej wyliczenia ze specyfikatorem „const” jest niedozwolona wartość „NaN”.", "const_enum_member_initializers_must_be_constant_expressions_2474": "inicjatory składowych wyliczenia const muszą być wyraż<PERSON>mi stałymi.", "const_enums_can_only_be_used_in_property_or_index_access_expressions_or_the_right_hand_side_of_an_im_2475": "Wyliczenia ze specyfikatorem „const” mogą być używane tylko w wyrażeniach dostępu do indeksu lub właściwości albo po prawej stronie deklaracji importu, przypisania eksportu lub typu zapytania.", "constructor_cannot_be_used_as_a_parameter_property_name_2398": "Nie można użyć ciągu „constructor” jako nazwy właściwości parametru.", "constructor_is_a_reserved_word_18012": "„#constructor” jest słowem zastrzeżonym.", "default_Colon_6903": "wartość domyślna:", "delete_cannot_be_called_on_an_identifier_in_strict_mode_1102": "Nie można wywołać elementu „delete” dla identyfikatora w trybie z ograniczeniami.", "export_Asterisk_does_not_re_export_a_default_1195": "Wyrażenie „export *” nie eksportuje ponownie eksportów domyślnych.", "export_can_only_be_used_in_TypeScript_files_8003": "Składnia „export =” może być używana tylko w plikach TypeScript.", "export_modifier_cannot_be_applied_to_ambient_modules_and_module_augmentations_since_they_are_always__2668": "Modyfikator „export” nie może być stosowany do modułów otoczenia ani rozszerzeń modułów, ponieważ są one zawsze widoczne.", "extends_clause_already_seen_1172": "Napotkano już klauzulę „extends”.", "extends_clause_must_precede_implements_clause_1173": "<PERSON><PERSON><PERSON><PERSON> „extends” musi poprzedzać klauzulę „implements”.", "extends_clause_of_exported_class_0_has_or_is_using_private_name_1_4020": "<PERSON><PERSON><PERSON><PERSON> „extends” wyeksportowanej klasy „{0}” ma nazwę prywatną „{1}” lub używa tej nazwy.", "extends_clause_of_exported_class_has_or_is_using_private_name_0_4021": "<PERSON><PERSON><PERSON><PERSON> „extends” wyeksportowanej klasy ma nazwę prywatną „{0}” lub używa tej nazwy.", "extends_clause_of_exported_interface_0_has_or_is_using_private_name_1_4022": "<PERSON><PERSON><PERSON><PERSON> „extends” wyeksportowanego interfejsu „{0}” ma nazwę prywatną „{1}” lub używa tej nazwy.", "false_unless_composite_is_set_6906": "\"false\", chyba że ustawiono element \"composite\"", "false_unless_strict_is_set_6905": "\"false\", chyba <PERSON>e ustawiono \"strict\"", "file_6025": "plik", "for_await_loops_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_file_1431": "Pętle „for await” na najwyższym poziomie pliku są dozwolone tylko wtedy, gdy plik jest modułem, ale ten plik nie ma żadnych importów ani eksportów. Rozważ dodanie pustego elementu „export {}”, aby ten plik był modułem.", "for_await_loops_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1103": "Pętle „for await” są dozwolone tylko w funkcjach asynchronicznych i na najwyższym poziomie modułów.", "for_await_loops_cannot_be_used_inside_a_class_static_block_18038": "<PERSON><PERSON><PERSON><PERSON> „for await” nie można używać wewnątrz bloku statycznego klasy.", "get_and_set_accessors_cannot_declare_this_parameters_2784": "<PERSON><PERSON> „get” i „set” nie mogą deklarować parametrów „this”.", "if_files_is_specified_otherwise_Asterisk_Asterisk_Slash_Asterisk_6908": "\"[]\" jeśli określono \"files\", w przeciwnym razie \"[\"**/*\"]5D;\"", "implements_clause_already_seen_1175": "Napotkano już klauzulę „implements”.", "implements_clauses_can_only_be_used_in_TypeScript_files_8005": "<PERSON><PERSON><PERSON><PERSON> „implements” mogą być używane tylko w plikach TypeScript.", "import_can_only_be_used_in_TypeScript_files_8002": "Ciąg „import ... =” może być używany tylko w plikach TypeScript.", "infer_declarations_are_only_permitted_in_the_extends_clause_of_a_conditional_type_1338": "Dekla<PERSON><PERSON> „infer” są dozwolone tylko w klauzuli „extends” typu warunkowego.", "k_must_be_followed_by_a_capturing_group_name_enclosed_in_angle_brackets_1510": "Po wyrażeniu „\\k” musi następować nazwa grupy przechwytywania ujęta w nawiasy ostre.", "let_is_not_allowed_to_be_used_as_a_name_in_let_or_const_declarations_2480": "Element „let” nie może być używany jako nazwa w deklaracjach „let” ani „const”.", "module_AMD_or_UMD_or_System_or_ES6_then_Classic_Otherwise_Node_69010": "module === \"AMD\" lub \"UMD\" lub \"System\" lub \"ES6\", a następnie \"Classic\", w przeciwnym razie \"Node\"", "module_system_or_esModuleInterop_6904": "module === \"system\" lub esModuleInterop", "new_expression_whose_target_lacks_a_construct_signature_implicitly_has_an_any_type_7009": "Wyrażenie „new”, którego element docelowy nie ma sygnatury konstrukcji, jest niejawnie typu „any”.", "node_modules_bower_components_jspm_packages_plus_the_value_of_outDir_if_one_is_specified_6907": "\"[\"node_modules\", \"bower_components\", \"jspm_packages\"]\", plus wartość elementu \"outDir\", jeś<PERSON> jest okre<PERSON>lona.", "one_of_Colon_6900": "jeden z:", "one_or_more_Colon_6901": "co najmniej jeden:", "options_6024": "op<PERSON><PERSON>", "or_JSX_element_expected_1145": "Oczekiwano elementu „{” lub JSX.", "or_expected_1144": "Oczekiwano znaku „{” lub „;”.", "package_json_does_not_have_a_0_field_6100": "Plik „package.json” nie zawiera pola „{0}”.", "package_json_does_not_have_a_typesVersions_entry_that_matches_version_0_6207": "Plik „package.json” nie zawiera wpisu „typesVersions” odpowiadającego wersji „{0}”.", "package_json_had_a_falsy_0_field_6220": "Plik „package.json” miał bł<PERSON> pole „{0}”.", "package_json_has_0_field_1_that_references_2_6101": "Plik „package.json” zawiera pole „{0}” „{1}” odwołujące się do elementu „{2}”.", "package_json_has_a_peerDependencies_field_6281": "Plik „package.json” ma pole „peerDependencies”.", "package_json_has_a_typesVersions_entry_0_that_is_not_a_valid_semver_range_6209": "Plik „package.json” ma wpis „{0}” pola „typesVersions”, który nie jest prawidłowym zakresem semver.", "package_json_has_a_typesVersions_entry_0_that_matches_compiler_version_1_looking_for_a_pattern_to_ma_6208": "Plik „package.json” ma wpis „{0}” pola „typesVersions”, który dopasowuje wersję kompilatora „{1}”, szukając wzorca odpowiadającego nazwie modułu „{2}”.", "package_json_has_a_typesVersions_field_with_version_specific_path_mappings_6206": "Plik „package.json” zawiera pole „typesVersions” z mapowaniami ścieżek specyficznymi dla wersji.", "package_json_scope_0_explicitly_maps_specifier_1_to_null_6274": "Zakres package.json „{0}” jawnie mapuje specyfikator „{1}” na wartość null.", "package_json_scope_0_has_invalid_type_for_target_of_specifier_1_6275": "Zakres package.json „{0}” ma nieprawidłowy typ elementu docelowego specyfikatora „{1}”", "package_json_scope_0_has_no_imports_defined_6273": "Zakres package.json „{0}” nie ma zdefiniowanych importów.", "paths_option_is_specified_looking_for_a_pattern_to_match_module_name_0_6091": "<PERSON><PERSON><PERSON> „paths” została określona. Wyszukiwanie wzorca zgodnego z nazwą modułu „{0}”.", "q_is_only_available_inside_character_class_1511": "Wyrażenie „\\q” jest dostępne tylko wewnątrz klasy znaków.", "q_must_be_followed_by_string_alternatives_enclosed_in_braces_1521": "Po wyrażeniu „\\q” muszą następować alternatywy ciągów ujęte w nawiasy klamrowe.", "readonly_modifier_can_only_appear_on_a_property_declaration_or_index_signature_1024": "<PERSON><PERSON><PERSON><PERSON><PERSON> „readonly” może występować jedynie w deklaracji właściwości lub sygnaturze indeksu.", "readonly_type_modifier_is_only_permitted_on_array_and_tuple_literal_types_1354": "Modyfikator typu „readonly” jest dozwolony tylko w typach literału tablicy i krotki.", "require_call_may_be_converted_to_an_import_80005": "Wywołanie „require” może zostać przekonwertowane na wywołanie import.", "resolution_mode_can_only_be_set_for_type_only_imports_1454": "Element „resolution-mode“ można ustawić wyłącznie dla importów tylko typów.", "resolution_mode_is_the_only_valid_key_for_type_import_assertions_1455": "Element „resolution-mode“ jest jed<PERSON>ym prawidłowym kluczem dla twierdzenia importu typu.", "resolution_mode_is_the_only_valid_key_for_type_import_attributes_1463": "Element „resolution-mode” jest jed<PERSON>ym prawidłowym kluczem dla atrybutów importu typu.", "resolution_mode_should_be_either_require_or_import_1453": "Element „resolution-mode” powinien mie<PERSON> warto<PERSON> „require” lub „import”.", "rootDirs_option_is_set_using_it_to_resolve_relative_module_name_0_6107": "<PERSON><PERSON>ja „rootDirs” została ustawiona. Zostanie ona użyta do rozpoznania względnej nazwy modułu „{0}”.", "super_can_only_be_referenced_in_a_derived_class_2335": "Element „super” może być przywoływany tylko w klasie pochodnej.", "super_can_only_be_referenced_in_members_of_derived_classes_or_object_literal_expressions_2660": "Element „super” może być przywoływany jedynie w składowych klas pochodnych lub wyrażeń literałów obiektów.", "super_cannot_be_referenced_in_a_computed_property_name_2466": "Nie można przywołać elementu „super” w obliczonej nazwie właściwości.", "super_cannot_be_referenced_in_constructor_arguments_2336": "Nie można przywoływać elementu „super” w argumentach konstruktora.", "super_is_only_allowed_in_members_of_object_literal_expressions_when_option_target_is_ES2015_or_highe_2659": "Element „super” jest dozwolony w składowych wyrażeń literałów obiektów tylko wtedy, gdy opcja „target” ma wartość „ES2015” lub wyższą.", "super_may_not_use_type_arguments_2754": "Element „super” nie może używać argumentów typu.", "super_must_be_called_before_accessing_a_property_of_super_in_the_constructor_of_a_derived_class_17011": "Element „super” należy wywołać przed uzyskaniem dostępu do właściwości elementu „super” w konstruktorze klasy pochodnej.", "super_must_be_called_before_accessing_this_in_the_constructor_of_a_derived_class_17009": "Element „super” musi być wywoływany przed uzyskaniem dostępu do elementu „this” w konstruktorze klasy pochodnej.", "super_must_be_followed_by_an_argument_list_or_member_access_1034": "Po elemencie „super” musi występować lista argumentów lub metoda dostępu do składowej.", "super_property_access_is_permitted_only_in_a_constructor_member_function_or_member_accessor_of_a_der_2338": "Dostęp do właściwości „super” jest dozwolony tylko w konstruktorze, funkcji składowej lub metodzie dostępu składowej klasy pochodnej.", "this_cannot_be_referenced_in_a_computed_property_name_2465": "Nie można przywołać elementu „this” w obliczonej nazwie właściwości.", "this_cannot_be_referenced_in_a_module_or_namespace_body_2331": "Nie można przywołać elementu „this” w treści modułu ani przestrzeni nazw.", "this_cannot_be_referenced_in_a_static_property_initializer_2334": "Nie można przywołać elementu „this” w inicjatorze właściwości statycznej.", "this_cannot_be_referenced_in_current_location_2332": "Nie można przywołać elementu „this” w bieżącej lokalizacji.", "this_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_2683": "Element „this” niejawnie przyjmuje typ „any”, ponieważ nie ma adnotacji typu.", "true_for_ES2022_and_above_including_ESNext_6930": "war<PERSON><PERSON>ć \"true\" dla ES2022 i powyżej, uwzględniając ESNext.", "true_if_composite_false_otherwise_6909": "\"true\", je<PERSON><PERSON> \"composite\", w przeciwnym razie \"false\"", "true_when_moduleResolution_is_node16_nodenext_or_bundler_otherwise_false_6411": "„true”, gdy element „moduleResolution” ma wartość „node16”, „nodenext” lub „bundler”; w przeciwnym razie „false”.", "tsc_Colon_The_TypeScript_Compiler_6922": "tsc: kompilator TypeScript", "type_Colon_6902": "typ:", "unique_symbol_types_are_not_allowed_here_1335": "<PERSON><PERSON> „unique symbol” nie są dozwolone w tym miej<PERSON>cu.", "unique_symbol_types_are_only_allowed_on_variables_in_a_variable_statement_1334": "Typy „unique symbol” są dozwolone tylko w zmiennych w instrukcji zmiennej.", "unique_symbol_types_may_not_be_used_on_a_variable_declaration_with_a_binding_name_1333": "Typów „unique symbol” nie można używać w deklaracji zmiennej z nazwą powiązania.", "use_strict_directive_cannot_be_used_with_non_simple_parameter_list_1347": "Dyrektywy „use strict” nie można używać z listą parametrów, które nie są parametrami prostymi.", "use_strict_directive_used_here_1349": "Dyrektywa „use strict” użyta w tym miej<PERSON>cu.", "with_statements_are_not_allowed_in_an_async_function_block_1300": "Instrukcje „with” są niedozwolone w bloku funkcji asynchronicznej.", "with_statements_are_not_allowed_in_strict_mode_1101": "Instrukcje „with” są niedozwolone w trybie z ograniczeniami.", "yield_expression_implicitly_results_in_an_any_type_because_its_containing_generator_lacks_a_return_t_7057": "Wyrażenie „yield” niejawnie zwraca wynik w postaci typu „any”, ponieważ w zawierającym generatorze brakuje adnotacji zwracanego typu.", "yield_expressions_cannot_be_used_in_a_parameter_initializer_2523": "Wyrażeń „yield” nie można używać w inicjatorze parametru."}