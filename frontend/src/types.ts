// User Types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'citizen' | 'government_official';
  location?: Location;
}

// Location Types
export interface Location {
  county: string;
  area: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

// Feedback Types
export interface FeedbackCategory {
  id: string;
  name: string;
  icon: string;
  subcategories: string[];
  description: string;
}

export interface Feedback {
  id: string;
  title: string;
  description: string;
  category: string;
  subcategory: string;
  location: Location;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'submitted' | 'reviewing' | 'in_progress' | 'resolved' | 'closed';
  sentiment?: 'positive' | 'negative' | 'neutral';
  isAnonymous: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface FeedbackForm {
  category: string;
  subcategory: string;
  title: string;
  description: string;
  location: Location;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  attachments: File[];
  isAnonymous: boolean;
  contactMethod: 'email' | 'phone' | 'whatsapp';
}

// Dashboard Types
export interface DashboardData {
  totalFeedback: number;
  pendingFeedback: number;
  resolvedFeedback: number;
  averageResponseTime: number;
  responseRate: number;
  citizenSatisfaction: number;
}

// Statistics Types
export interface Statistic {
  value: string;
  label: string;
  trend?: 'up' | 'down' | 'stable';
}

// Navigation Types
export interface NavItem {
  name: string;
  href: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
}

// Feature Types
export interface Feature {
  number: string;
  title: string;
  description: string;
  icon?: string;
}

// Registration and Authentication Types
export interface County {
  id: number;
  name: string;
  code: string;
  is_active: boolean;
  location_data: {
    id: number;
    name: string;
    type: string;
    level: number;
    code: string;
    full_path: string;
  };
}

export interface LocationHierarchy {
  id: number;
  name: string;
  type: 'county' | 'sub_county' | 'ward' | 'village';
  level: number;
  code: string;
  full_path: string;
  children: LocationHierarchy[];
}

export interface RegistrationData {
  national_id: string;
  name: string;
  email: string;
  password: string;
  county_id: number;
  sub_county_id?: number;
  ward_id?: number;
  village_id?: number;
}

export interface AuthUser {
  id: number;
  name: string;
  email: string;
  role: string;
  role_display: string;
  county_name: string;
  date_joined: string;
}

export interface AuthTokens {
  access: string;
  refresh: string;
}

export interface RegistrationResponse {
  success: boolean;
  message: string;
  user?: AuthUser;
  tokens?: AuthTokens;
  errors?: Record<string, string[]>;
}

export interface LocationResponse {
  success: boolean;
  locations: LocationHierarchy[];
  message?: string;
}

export interface FormErrors {
  national_id?: string;
  name?: string;
  email?: string;
  password?: string;
  county_id?: string;
  general?: string;
}