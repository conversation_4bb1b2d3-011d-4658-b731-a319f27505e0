import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Header from './components/Header';
import Hero from './components/Hero';
import Services from './components/Services';
import WhyChooseUs from './components/WhyChooseUs';
import Statistics from './components/Statistics';
import Footer from './components/Footer';
import RegisterPage from './pages/RegisterPage';
import DashboardPage from './pages/DashboardPage';

// Home Page Component
const HomePage: React.FC = () => (
  <div className="min-h-screen bg-white">
    <Header />
    <Hero />
    <Services />
    <WhyChooseUs />
    <Statistics />
    <Footer />
  </div>
);

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/register" element={<RegisterPage />} />
        <Route path="/dashboard" element={<DashboardPage />} />
      </Routes>
    </Router>
  );
}

export default App;