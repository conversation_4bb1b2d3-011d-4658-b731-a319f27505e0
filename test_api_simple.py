#!/usr/bin/env python3
"""
Simple API test to check what the counties endpoint returns
"""

import requests
import json

def test_counties():
    try:
        response = requests.get("http://127.0.0.1:8000/api/locations/counties/")
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Content Type: {response.headers.get('content-type', 'Unknown')}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Response Type: {type(data)}")
                print(f"Response Length: {len(data) if hasattr(data, '__len__') else 'N/A'}")
                print(f"First 500 chars: {str(data)[:500]}")
                
                if isinstance(data, list) and len(data) > 0:
                    print(f"First item: {data[0]}")
                    print(f"First item keys: {list(data[0].keys()) if isinstance(data[0], dict) else 'Not a dict'}")
                
            except json.JSONDecodeError as e:
                print(f"JSON Decode Error: {e}")
                print(f"Raw content: {response.text[:500]}")
        else:
            print(f"Error response: {response.text}")
            
    except Exception as e:
        print(f"Request failed: {e}")

if __name__ == "__main__":
    test_counties()
