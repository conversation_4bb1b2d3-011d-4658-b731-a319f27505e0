# 🇰🇪 CivicAI User Registration System

## Overview

This document provides comprehensive documentation for the CivicAI user registration system, including both frontend (React/TypeScript) and backend (Django REST Framework) components.

## System Architecture

### Frontend Components
- **React + TypeScript** with Vite build system
- **TailwindCSS** for styling
- **React Router** for navigation
- **Custom API Service** for backend communication

### Backend Components
- **Django REST Framework** with JWT authentication
- **PostgreSQL** database with multi-tenant architecture
- **County-based tenancy** system
- **Location hierarchy** (County → Sub-County → Ward → Village)

## 🚀 Quick Start

### 1. Start the Backend Server
```bash
cd /path/to/civicAI
python manage.py runserver
```
Backend will be available at: `http://127.0.0.1:8000`

### 2. Start the Frontend Server
```bash
cd /path/to/civicAI/frontend
npm run dev
```
Frontend will be available at: `http://localhost:5174`

### 3. Access the Registration Page
Navigate to: `http://localhost:5174/register`

## 📋 Registration Form Features

### Required Fields
- **National ID**: 8-digit Kenyan National ID (e.g., `12345678`)
- **Full Name**: User's complete name as on National ID
- **Email**: Valid email address for notifications
- **Password**: Minimum 6 characters
- **County**: User's county (required for tenant assignment)

### Optional Fields
- **Sub-County**: Cascading dropdown based on selected county
- **Ward**: Cascading dropdown based on selected sub-county
- **Village**: Cascading dropdown based on selected ward

### Validation Features
- ✅ Real-time National ID format validation (exactly 8 digits)
- ✅ Email format validation
- ✅ Password strength requirements
- ✅ Dynamic location dropdowns with loading states
- ✅ Comprehensive error handling and user feedback
- ✅ Password visibility toggle
- ✅ Mobile-responsive design

## 🔧 API Endpoints

### 1. Get Counties
```http
GET /api/locations/counties/
```

**Response:**
```json
{
  "count": 47,
  "next": "http://127.0.0.1:8000/api/locations/counties/?page=2",
  "previous": null,
  "results": [
    {
      "id": 1,
      "name": "Kisumu",
      "code": "KSM",
      "is_active": true,
      "location_data": {
        "id": 1,
        "name": "Kisumu",
        "type": "county",
        "level": 0,
        "code": "001",
        "full_path": "Kisumu"
      }
    }
  ]
}
```

### 2. Get Location Hierarchy
```http
GET /api/locations/hierarchy/?county_id=1&type=sub_county
GET /api/locations/hierarchy/?parent_id=5&type=ward
GET /api/locations/hierarchy/?parent_id=15&type=village
```

**Response:**
```json
{
  "success": true,
  "locations": [
    {
      "id": 3,
      "name": "Kisumu East",
      "type": "sub_county",
      "level": 1,
      "code": "001-001",
      "full_path": "Kisumu > Kisumu East",
      "children": []
    }
  ]
}
```

### 3. User Registration
```http
POST /api/auth/register/
Content-Type: application/json

{
  "national_id": "12345678",
  "name": "John Doe Kiprop",
  "email": "<EMAIL>",
  "password": "securepass123",
  "county_id": 1,
  "sub_county_id": 5,
  "ward_id": 12,
  "village_id": 50
}
```

**Success Response (201):**
```json
{
  "success": true,
  "message": "Registration successful",
  "user": {
    "id": 1,
    "name": "John Doe Kiprop",
    "email": "<EMAIL>",
    "role": "citizen",
    "role_display": "Citizen",
    "county_name": "Kisumu",
    "date_joined": "2024-01-15T10:30:00Z"
  },
  "tokens": {
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
}
```

**Error Response (400):**
```json
{
  "success": false,
  "message": "Registration failed",
  "errors": {
    "national_id": ["National ID must be exactly 8 digits"],
    "email": ["User with this email already exists"]
  }
}
```

## 🧪 Testing the System

### Manual Testing Steps

1. **Navigate to Registration Page**
   - Go to `http://localhost:5174/register`
   - Verify the form loads correctly

2. **Test National ID Validation**
   - Enter invalid National ID (e.g., `123456` - only 6 digits)
   - Verify error message appears
   - Enter valid National ID (e.g., `12345678`)
   - Verify error clears

3. **Test Location Dropdowns**
   - Select a county from dropdown
   - Verify sub-counties load automatically
   - Select a sub-county
   - Verify wards load automatically
   - Select a ward
   - Verify villages load automatically

4. **Test Registration Flow**
   - Fill all required fields with valid data
   - Submit the form
   - Verify success message and redirect to dashboard

### Automated Testing Script

Run the provided test script:
```bash
python test_registration.py
```

This script tests:
- ✅ API health check
- ✅ Counties endpoint
- ✅ Location hierarchy endpoint
- ✅ Valid user registration
- ✅ Invalid National ID validation

## 🔐 Security Features

### Frontend Security
- **Input Validation**: Client-side validation for all form fields
- **XSS Prevention**: Proper input sanitization
- **HTTPS Ready**: Secure token storage in localStorage
- **CORS Protection**: Configured for specific origins

### Backend Security
- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: bcrypt for National ID and password hashing
- **Rate Limiting**: API throttling to prevent abuse
- **CSRF Protection**: Cross-site request forgery protection
- **SQL Injection Prevention**: Django ORM protection

## 🏗️ File Structure

### Frontend Files
```
frontend/src/
├── components/
│   ├── RegistrationForm.tsx    # Main registration form component
│   └── Header.tsx              # Updated with registration link
├── pages/
│   ├── RegisterPage.tsx        # Registration page wrapper
│   └── DashboardPage.tsx       # Post-registration dashboard
├── services/
│   └── api.ts                  # API service for backend calls
├── types.ts                    # TypeScript type definitions
└── App.tsx                     # Main app with routing
```

### Backend Files
```
apps/
├── api/
│   ├── views.py               # Registration and location API views
│   ├── serializers.py         # Data validation and serialization
│   └── urls.py                # API URL routing
├── users/
│   ├── models.py              # User and location models
│   └── utils.py               # National ID validation utilities
└── core/
    └── middleware.py          # Tenant isolation middleware
```

## 🚀 Deployment Notes

### Environment Variables
Create `.env` file in project root:
```env
SECRET_KEY=your-secret-key-here
DATABASE_URL=postgresql://user:password@localhost:5432/civicai
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1
TIME_ZONE=Africa/Nairobi
```

### Frontend Environment
Create `frontend/.env`:
```env
VITE_API_URL=http://127.0.0.1:8000
```

### Production Considerations
- Set `DEBUG=False` in production
- Configure proper CORS origins
- Use HTTPS for all communications
- Set up proper database backups
- Configure email backend for notifications
- Set up monitoring and logging

## 📞 Support

For technical support or questions about the registration system:
- Check the API documentation at: `http://127.0.0.1:8000/api/docs/`
- Review the test scripts for usage examples
- Check Django admin at: `http://127.0.0.1:8000/admin/`

## 💻 Sample Code Examples

### Frontend Integration Example

```typescript
// Using the API service in a React component
import { apiService } from '../services/api';
import { useState, useEffect } from 'react';

const MyRegistrationComponent = () => {
  const [counties, setCounties] = useState([]);
  const [loading, setLoading] = useState(false);

  // Load counties on component mount
  useEffect(() => {
    const loadCounties = async () => {
      try {
        setLoading(true);
        const countiesData = await apiService.getCounties();
        setCounties(countiesData);
      } catch (error) {
        console.error('Failed to load counties:', error);
      } finally {
        setLoading(false);
      }
    };

    loadCounties();
  }, []);

  // Handle registration
  const handleRegistration = async (formData) => {
    try {
      const response = await apiService.register({
        national_id: formData.nationalId,
        name: formData.name,
        email: formData.email,
        password: formData.password,
        county_id: formData.countyId
      });

      if (response.success) {
        console.log('Registration successful!', response.user);
        // Redirect to dashboard
        window.location.href = '/dashboard';
      }
    } catch (error) {
      console.error('Registration failed:', error);
    }
  };

  return (
    <div>
      {/* Your registration form JSX here */}
    </div>
  );
};
```

### Backend API Usage Example

```python
# Django view example for custom registration logic
from rest_framework.decorators import api_view
from rest_framework.response import Response
from apps.api.serializers import RegisterSerializer

@api_view(['POST'])
def custom_register(request):
    """Custom registration endpoint with additional logic"""
    serializer = RegisterSerializer(data=request.data)

    if serializer.is_valid():
        # Custom validation logic here
        national_id = serializer.validated_data['national_id']

        # Check if National ID is valid format
        if not re.match(r'^\d{8}$', national_id):
            return Response({
                'success': False,
                'message': 'Invalid National ID format'
            }, status=400)

        # Create user
        user = serializer.save()

        # Generate tokens
        from rest_framework_simplejwt.tokens import RefreshToken
        refresh = RefreshToken.for_user(user)

        return Response({
            'success': True,
            'message': 'Registration successful',
            'user': {
                'id': user.id,
                'name': user.name,
                'email': user.email,
                'county': user.tenant.name
            },
            'tokens': {
                'access': str(refresh.access_token),
                'refresh': str(refresh)
            }
        }, status=201)

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=400)
```

### cURL Testing Examples

```bash
# Test counties endpoint
curl -X GET "http://127.0.0.1:8000/api/locations/counties/" \
  -H "Content-Type: application/json"

# Test location hierarchy
curl -X GET "http://127.0.0.1:8000/api/locations/hierarchy/?county_id=1&type=sub_county" \
  -H "Content-Type: application/json"

# Test user registration
curl -X POST "http://127.0.0.1:8000/api/auth/register/" \
  -H "Content-Type: application/json" \
  -d '{
    "national_id": "12345678",
    "name": "Test User Kiprop",
    "email": "<EMAIL>",
    "password": "testpass123",
    "county_id": 1
  }'

# Test login after registration
curl -X POST "http://127.0.0.1:8000/api/auth/login/" \
  -H "Content-Type: application/json" \
  -d '{
    "national_id": "12345678",
    "password": "testpass123"
  }'
```

## 🎉 Success Criteria

The registration system is working correctly when:
- ✅ Counties load in dropdown without errors
- ✅ Location hierarchy cascades properly
- ✅ National ID validation works (8 digits only)
- ✅ Registration creates user and returns JWT tokens
- ✅ User is redirected to dashboard after successful registration
- ✅ Error messages display clearly for validation failures
- ✅ System handles network errors gracefully

## 🔄 Next Steps

After successful registration implementation, consider:
1. **Email Verification**: Add email confirmation flow
2. **SMS Verification**: Integrate with Kenyan SMS providers
3. **Social Login**: Add Google/Facebook authentication
4. **Profile Management**: User profile editing capabilities
5. **Password Reset**: Forgot password functionality
6. **Admin Dashboard**: County admin user management
7. **Analytics**: Registration metrics and reporting
