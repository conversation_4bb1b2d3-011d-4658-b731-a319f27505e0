#!/usr/bin/env python3
"""
Test script for CivicAI Registration API
Tests the complete registration flow with Kenyan National ID validation
"""

import requests
import json
import sys

# API Configuration
BASE_URL = "http://127.0.0.1:8000"
HEADERS = {"Content-Type": "application/json"}

def test_api_health():
    """Test if the API is running"""
    try:
        response = requests.get(f"{BASE_URL}/api/health/")
        if response.status_code == 200:
            data = response.json()
            print("✅ API Health Check: PASSED")
            print(f"   Version: {data.get('version', 'Unknown')}")
            print(f"   Counties: {data.get('features', {}).get('counties', 'Unknown')}")
            return True
        else:
            print(f"❌ API Health Check: FAILED (Status: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ API Health Check: FAILED (Error: {e})")
        return False

def test_counties_endpoint():
    """Test counties endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/api/locations/counties/")
        if response.status_code == 200:
            counties = response.json()
            print(f"✅ Counties Endpoint: PASSED ({len(counties)} counties found)")
            if counties:
                print(f"   Sample: {counties[0]['name']} ({counties[0]['code']})")
            return counties
        else:
            print(f"❌ Counties Endpoint: FAILED (Status: {response.status_code})")
            print(f"   Response: {response.text}")
            return []
    except Exception as e:
        print(f"❌ Counties Endpoint: FAILED (Error: {e})")
        return []

def test_location_hierarchy(county_id):
    """Test location hierarchy endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/api/locations/hierarchy/?county_id={county_id}&type=sub_county")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                locations = data.get('locations', [])
                print(f"✅ Location Hierarchy: PASSED ({len(locations)} sub-counties found)")
                return locations
            else:
                print(f"❌ Location Hierarchy: FAILED ({data.get('message', 'Unknown error')})")
                return []
        else:
            print(f"❌ Location Hierarchy: FAILED (Status: {response.status_code})")
            return []
    except Exception as e:
        print(f"❌ Location Hierarchy: FAILED (Error: {e})")
        return []

def test_registration():
    """Test user registration"""
    # Get a county first
    counties = test_counties_endpoint()
    if not counties:
        print("❌ Cannot test registration without counties")
        return False
    
    county_id = counties[0]['id']
    
    # Test registration data
    registration_data = {
        "national_id": "12345678",
        "name": "Test User Kiprop",
        "email": "<EMAIL>",
        "password": "testpass123",
        "county_id": county_id
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/register/",
            headers=HEADERS,
            data=json.dumps(registration_data)
        )
        
        data = response.json()
        
        if response.status_code == 201 and data.get('success'):
            print("✅ Registration: PASSED")
            print(f"   User: {data['user']['name']}")
            print(f"   Email: {data['user']['email']}")
            print(f"   County: {data['user']['county_name']}")
            print(f"   Role: {data['user']['role_display']}")
            print(f"   Access Token: {'✓' if data.get('tokens', {}).get('access') else '✗'}")
            return True
        else:
            print(f"❌ Registration: FAILED")
            print(f"   Status: {response.status_code}")
            print(f"   Message: {data.get('message', 'Unknown error')}")
            if data.get('errors'):
                print(f"   Errors: {data['errors']}")
            return False
            
    except Exception as e:
        print(f"❌ Registration: FAILED (Error: {e})")
        return False

def test_invalid_national_id():
    """Test registration with invalid National ID"""
    counties = test_counties_endpoint()
    if not counties:
        return False
    
    county_id = counties[0]['id']
    
    # Test with invalid National ID (not 8 digits)
    registration_data = {
        "national_id": "123456",  # Only 6 digits
        "name": "Test User Invalid",
        "email": "<EMAIL>",
        "password": "testpass123",
        "county_id": county_id
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/register/",
            headers=HEADERS,
            data=json.dumps(registration_data)
        )
        
        data = response.json()
        
        if response.status_code == 400 and not data.get('success'):
            print("✅ Invalid National ID Validation: PASSED")
            print(f"   Correctly rejected: {data.get('message', 'Invalid data')}")
            return True
        else:
            print(f"❌ Invalid National ID Validation: FAILED")
            print(f"   Should have been rejected but got status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Invalid National ID Validation: FAILED (Error: {e})")
        return False

def main():
    """Run all tests"""
    print("🇰🇪 CivicAI Registration API Test Suite")
    print("=" * 50)

    tests_passed = 0
    total_tests = 5

    # Test 1: API Health
    if test_api_health():
        tests_passed += 1

    print()

    # Test 2: Counties Endpoint
    counties = test_counties_endpoint()
    if counties:
        tests_passed += 1

        print()

        # Test 3: Location Hierarchy
        if test_location_hierarchy(counties[0]['id']):
            tests_passed += 1

        print()

        # Test 4: Valid Registration
        if test_registration():
            tests_passed += 1

        print()

        # Test 5: Invalid National ID
        if test_invalid_national_id():
            tests_passed += 1
    else:
        print("❌ Skipping remaining tests due to counties endpoint failure")

    print()
    print("=" * 50)
    print(f"Tests Passed: {tests_passed}/{total_tests}")

    if tests_passed == total_tests:
        print("🎉 All tests passed! Registration system is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
